#!/usr/bin/env python3
"""
Celery worker启动脚本
"""

from celery import Celery
from config import CELERY_BROKER_URL, CELERY_RESULT_BACKEND

# 创建Celery应用
celery_app = Celery('webdav_service')

# 配置Celery
celery_app.conf.update(
    broker_url=CELERY_BROKER_URL,
    result_backend=CELERY_RESULT_BACKEND,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_routes={
        'app.upload_task': {'queue': 'webdav_upload'}
    },
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=50
)

# 自动发现任务
celery_app.autodiscover_tasks(['app'])

if __name__ == '__main__':
    celery_app.start()
