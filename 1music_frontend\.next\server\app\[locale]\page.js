/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ar.json": [
		"(rsc)/./messages/ar.json",
		"_rsc_messages_ar_json"
	],
	"./de.json": [
		"(rsc)/./messages/de.json",
		"_rsc_messages_de_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./fr.json": [
		"(rsc)/./messages/fr.json",
		"_rsc_messages_fr_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./it.json": [
		"(rsc)/./messages/it.json",
		"_rsc_messages_it_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./ko.json": [
		"(rsc)/./messages/ko.json",
		"_rsc_messages_ko_json"
	],
	"./nl.json": [
		"(rsc)/./messages/nl.json",
		"_rsc_messages_nl_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./ru.json": [
		"(rsc)/./messages/ru.json",
		"_rsc_messages_ru_json"
	],
	"./tr.json": [
		"(rsc)/./messages/tr.json",
		"_rsc_messages_tr_json"
	],
	"./zh-CN.json": [
		"(rsc)/./messages/zh-CN.json",
		"_rsc_messages_zh-CN_json"
	],
	"./zh-TW.json": [
		"(rsc)/./messages/zh-TW.json",
		"_rsc_messages_zh-TW_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.js */ \"(rsc)/./src/app/[locale]/layout.js\"));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.js */ \"(rsc)/./src/app/[locale]/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\"],\n          \n        }]\n      },\n        {\n        'layout': [module3, \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\"],\n        \n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js */ \"(ssr)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5C%40mui%5C%5Cmaterial-nextjs%5C%5Cv13-appRouter%5C%5CappRouterV13.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.js */ \"(rsc)/./src/app/[locale]/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0XFxcXHdlYlxcXFwxbXVzaWNcXFxcMW11c2ljX2Zyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcW2xvY2FsZV1cXFxccGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.js */ \"(ssr)/./src/app/[locale]/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0JTVDJTVDd2ViJTVDJTVDMW11c2ljJTVDJTVDMW11c2ljX2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0XFxcXHdlYlxcXFwxbXVzaWNcXFxcMW11c2ljX2Zyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcW2xvY2FsZV1cXFxccGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5Cweb%5C%5C1music%5C%5C1music_frontend%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/component/audioPlayer.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/component/audioPlayer.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardMedia,CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardMedia,CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardMedia,CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardMedia,CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CardMedia/CardMedia.js\");\n/* harmony import */ var _src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/app/[locale]/utils */ \"(ssr)/./src/app/[locale]/utils.js\");\n\n\n\n\nfunction AudioPlayer({ coverHeight, songData }) {\n    const [audioLoading, setAudioLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('https://picsum.photos/200/200');\n    const [audioSrc, setAudioSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    let isMounted = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const handlePreview = async ()=>{\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        const controller = new AbortController();\n        abortControllerRef.current = controller;\n        const { signal } = controller;\n        setAudioLoading(true);\n        let retries = 0;\n        const maxRetries = 20;\n        const data = {\n            title: songData.title,\n            album: songData.album,\n            artist: songData.artist,\n            videoId: songData.videoId,\n            request_format: 'webm',\n            song_hash: songData.song_hash\n        };\n        try {\n            while(retries < maxRetries){\n                if (signal.aborted) return;\n                const status = await (0,_src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__.fetchDownloadStatus)(data, 'preview', {\n                    signal\n                });\n                if (status.download_url) {\n                    let linkReadyRetries = 0;\n                    while(linkReadyRetries < maxRetries){\n                        if (signal.aborted) return;\n                        const isReady = await (0,_src_app_locale_utils__WEBPACK_IMPORTED_MODULE_2__.checkDownloadLink)(status.download_url, {\n                            signal\n                        });\n                        if (isReady) {\n                            if (!isMounted.current || signal.aborted) return;\n                            setAudioSrc(status.download_url);\n                            setCoverImageUrl(songData.thumbnail);\n                            setAudioLoading(false);\n                            return;\n                        }\n                        await new Promise((resolve)=>setTimeout(resolve, 2000));\n                        linkReadyRetries++;\n                    }\n                    throw new Error('下载链接暂时不可用，请稍后重试。');\n                }\n                await new Promise((resolve)=>setTimeout(resolve, 2000));\n                retries++;\n            }\n            throw new Error('获取下载链接超时，请稍后重试。');\n        } catch (err) {\n            if (err.name === 'AbortError') {\n                console.log('请求被中止。');\n            } else {\n                console.error('预览失败：', err);\n            }\n            if (isMounted.current) setAudioLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioPlayer.useEffect\": ()=>{\n            if (songData) {\n                handlePreview();\n            }\n            return ({\n                \"AudioPlayer.useEffect\": ()=>{\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"AudioPlayer.useEffect\"];\n        }\n    }[\"AudioPlayer.useEffect\"], [\n        songData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioPlayer.useEffect\": ()=>{\n            isMounted.current = true;\n            return ({\n                \"AudioPlayer.useEffect\": ()=>{\n                    isMounted.current = false;\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"AudioPlayer.useEffect\"];\n        }\n    }[\"AudioPlayer.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioPlayer.useEffect\": ()=>{\n            const audioElement = document.getElementById('audio-player');\n            if (audioElement) {\n                audioElement.load();\n                audioElement.play().catch({\n                    \"AudioPlayer.useEffect\": (error)=>{\n                        console.warn('自动播放被阻止:', error);\n                    }\n                }[\"AudioPlayer.useEffect\"]);\n            }\n        }\n    }[\"AudioPlayer.useEffect\"], [\n        audioSrc\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        sx: {\n            display: 'flex',\n            alignItems: 'center',\n            height: coverHeight,\n            width: '100%',\n            position: 'fixed',\n            bottom: 0,\n            left: 0,\n            zIndex: 1000,\n            borderRadius: 0\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    height: '100%',\n                    width: coverHeight,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    backgroundColor: '#f0f0f0'\n                },\n                children: audioLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    size: 40\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\audioPlayer.js\",\n                    lineNumber: 127,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    component: \"img\",\n                    image: coverImageUrl,\n                    alt: \"Album cover\",\n                    sx: {\n                        height: '100%',\n                        width: '100%',\n                        objectFit: 'cover',\n                        margin: 0\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\audioPlayer.js\",\n                    lineNumber: 129,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\audioPlayer.js\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardMedia_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    flexGrow: 1,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    padding: '0 16px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                    loop: true,\n                    id: \"audio-player\",\n                    controls: true,\n                    style: {\n                        width: '100%'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                            src: audioSrc,\n                            type: \"audio/webm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\audioPlayer.js\",\n                            lineNumber: 153,\n                            columnNumber: 21\n                        }, this),\n                        \"Your browser does not support the audio element.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\audioPlayer.js\",\n                    lineNumber: 152,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\audioPlayer.js\",\n                lineNumber: 143,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\audioPlayer.js\",\n        lineNumber: 103,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/component/audioPlayer.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/component/musicContainer.js":
/*!******************************************************!*\
  !*** ./src/app/[locale]/component/musicContainer.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MusicCardContainer: () => (/* binding */ MusicCardContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/RadioGroup/RadioGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Radio/Radio.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CardMedia/CardMedia.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,CardMedia,CircularProgress,Container,Dialog,DialogActions,DialogContent,DialogTitle,FormControlLabel,Grid,IconButton,Radio,RadioGroup,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _mui_icons_material_Download__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/Download */ \"(ssr)/./node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _toolpad_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @toolpad/core */ \"(ssr)/./node_modules/@toolpad/core/esm/useNotifications/useNotifications.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(ssr)/./src/app/[locale]/config.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _src_app_locale_page__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/page */ \"(ssr)/./src/app/[locale]/page.js\");\n\n\n\n\n\n\n\n\n\nconst DownloadDialog = ({ open, onClose, song })=>{\n    const [format, setFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('mp3');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const notifications = (0,_toolpad_core__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const { webdavConfigs, premium } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_src_app_locale_page__WEBPACK_IMPORTED_MODULE_3__.GlobalContext);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)(\"Download\");\n    const handleDownload = ()=>{\n        // Open new tab with query string\n        const queryParams = new URLSearchParams({\n            title: song.title,\n            album: song.album,\n            artist: song.artist,\n            videoId: song.videoId,\n            request_format: format,\n            song_hash: song.song_hash,\n            thumbnail: song.thumbnail\n        }).toString();\n        window.open(`/download?${queryParams}`, '_blank');\n        onClose();\n    };\n    const handleWebDavUpload = async ()=>{\n        if (!selectedConfigId) {\n            alert(t(\"select_webdav\"));\n            return;\n        }\n        if (!song || !song.title || !song.album || !song.artist || !song.videoId) {\n            alert(t(\"incomplete_song_info\"));\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        const songData = {\n            title: song.title,\n            album: song.album,\n            artist: song.artist,\n            videoId: song.videoId,\n            request_format: format,\n            song_hash: song.song_hash,\n            thumbnail: song.thumbnail,\n            config_id: selectedConfigId\n        };\n        try {\n            const response = await fetch(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_2__.backendUrl + 'upload_to_webdav/', {\n                method: 'POST',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(songData)\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || result.detail || t(\"upload_error\"));\n            }\n            notifications.show(t(\"upload_success\", {\n                title: song.title\n            }), {\n                autoHideDuration: 2000\n            });\n            setIsLoading(false);\n            onClose();\n        } catch (err) {\n            setError(err.message);\n            setIsLoading(false);\n        }\n    };\n    const handleWebDavConfigChange = (e)=>{\n        const configId = e.target.value;\n        const selected = webdavConfigs.find((config)=>config.id === configId);\n        setSelectedConfig(selected);\n        setSelectedConfigId(configId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        onClick: (event)=>event.stopPropagation(),\n        open: open,\n        onClose: onClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                children: t(\"download_song\", {\n                    title: song.title\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 112,\n                columnNumber: 13\n            }, undefined),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                height: \"80px\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                    lineNumber: 114,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 113,\n                columnNumber: 25\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        value: format,\n                        onChange: (e)=>setFormat(e.target.value),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                value: \"mp3\",\n                                control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 60\n                                }, void 0),\n                                label: \"MP3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                lineNumber: 117,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                value: \"flac\",\n                                control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 61\n                                }, void 0),\n                                label: \"FLAC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                lineNumber: 118,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 116,\n                        columnNumber: 17\n                    }, undefined),\n                    webdavConfigs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                variant: \"subtitle1\",\n                                children: t(\"select_webdav_config\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                lineNumber: 122,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                value: selectedConfigId || '',\n                                onChange: handleWebDavConfigChange,\n                                children: webdavConfigs.map((config, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        value: config.id,\n                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                            lineNumber: 125,\n                                            columnNumber: 90\n                                        }, void 0),\n                                        label: config.url\n                                    }, index, false, {\n                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 33\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                lineNumber: 123,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        color: \"error\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 130,\n                        columnNumber: 27\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 115,\n                columnNumber: 22\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        \"aria-label\": premium ? \"premium\" : \"no_premium\",\n                        onClick: handleDownload,\n                        disabled: isLoading,\n                        children: t(\"download\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 133,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: handleWebDavUpload,\n                        disabled: isLoading || webdavConfigs.length < 1,\n                        children: t(\"upload_to_webdav\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 134,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: onClose,\n                        disabled: isLoading,\n                        children: t(\"cancel\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 135,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 132,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n        lineNumber: 111,\n        columnNumber: 9\n    }, undefined);\n};\n// 音乐卡片组件\nconst MusicCard = ({ coverHeight, song })=>{\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { setPlayingSongData } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_src_app_locale_page__WEBPACK_IMPORTED_MODULE_3__.GlobalContext);\n    const previewHandle = ()=>{\n        setPlayingSongData(song);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n        sx: {\n            minWidth: 275,\n            borderRadius: 2,\n            boxShadow: 'none',\n            display: 'flex',\n            alignItems: 'center',\n            backgroundColor: 'theme.palette.background.paper',\n            border: '1px solid #e0e0e0',\n            overflow: 'hidden',\n            margin: 0.5,\n            position: 'relative',\n            '&:hover': {\n                backgroundColor: 'theme.palette.background.paper',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n            }\n        },\n        onClick: ()=>previewHandle(),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                component: \"img\",\n                sx: {\n                    width: coverHeight,\n                    height: coverHeight,\n                    objectFit: 'cover',\n                    borderRadius: 1,\n                    margin: 1,\n                    flexShrink: 0\n                },\n                image: song.thumbnail,\n                alt: song.title\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 168,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    padding: 1.5,\n                    paddingRight: '48px',\n                    minWidth: 0,\n                    overflow: 'hidden' // Ensure content doesn't overflow\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        variant: \"h6\",\n                        sx: {\n                            color: 'theme.palette.text.primary',\n                            fontWeight: 'bold',\n                            fontSize: '1rem',\n                            lineHeight: 1.4,\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            whiteSpace: 'nowrap'\n                        },\n                        children: song.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 190,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        variant: \"body2\",\n                        sx: {\n                            color: '#666',\n                            marginTop: 0.5,\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            whiteSpace: 'nowrap'\n                        },\n                        children: [\n                            song.artist,\n                            \" \\xb7 \",\n                            song.album\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 204,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        variant: \"caption\",\n                        sx: {\n                            color: '#999',\n                            marginTop: 0.5\n                        },\n                        children: song.duration\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                        lineNumber: 216,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 181,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                color: \"inherit\",\n                sx: {\n                    position: 'absolute',\n                    top: '25%',\n                    right: coverHeight === 80 ? 4 : 1,\n                    transform: 'translateY(-50%)',\n                    zIndex: 10\n                },\n                onClick: (e)=>{\n                    setDialogOpen(true);\n                    e.stopPropagation();\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Download__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                    lineNumber: 234,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 220,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DownloadDialog, {\n                open: dialogOpen,\n                onClose: ()=>setDialogOpen(false),\n                song: song\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 236,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n        lineNumber: 149,\n        columnNumber: 9\n    }, undefined);\n};\n// 音乐卡片容器组件\nconst MusicCardContainer = ({ songs, loading })=>{\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"])();\n    // 根据屏幕宽度动态设置列数\n    const isSmallScreen = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"])(theme.breakpoints.down('sm'));\n    const isMediumScreen = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"])(theme.breakpoints.between('sm', 'md'));\n    const columns = isSmallScreen ? 1 : isMediumScreen ? 2 : 3;\n    const coverHeight = isSmallScreen ? 60 : 80;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n        sx: {\n            position: 'relative'\n        },\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: (theme)=>({\n                        position: 'absolute',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        zIndex: 10,\n                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.6)',\n                        display: 'flex',\n                        justifyContent: 'center',\n                        alignItems: 'flex-start' // 修改为顶部对齐\n                    }),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        marginTop: 16\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                    lineNumber: 268,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 254,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    flexGrow: 1,\n                    mt: 1,\n                    opacity: loading ? 0.5 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                    container: true,\n                    spacing: 0.5,\n                    children: songs.map((song, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_CardMedia_CircularProgress_Container_Dialog_DialogActions_DialogContent_DialogTitle_FormControlLabel_Grid_IconButton_Radio_RadioGroup_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            item: true,\n                            xs: 12 / columns,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MusicCard, {\n                                coverHeight: coverHeight,\n                                song: song\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                                lineNumber: 275,\n                                columnNumber: 29\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                            lineNumber: 274,\n                            columnNumber: 25\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                    lineNumber: 272,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n                lineNumber: 271,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\musicContainer.js\",\n        lineNumber: 252,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/component/musicContainer.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   webdavServiceUrl: () => (/* binding */ webdavServiceUrl)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst webdavServiceUrl = 'http://localhost:5001/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTyxNQUFNQSxhQUFhLHFDQUFvQztBQUN2RCxNQUFNQyxtQkFBbUIseUJBQXdCO0FBRWpELE1BQU1DLFlBQVk7SUFDckIsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLFNBQVM7SUFDVCxTQUFTO0FBQ2IsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXHdlYlxcMW11c2ljXFwxbXVzaWNfZnJvbnRlbmRcXHNyY1xcYXBwXFxbbG9jYWxlXVxcY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBiYWNrZW5kVXJsID0gJ2h0dHA6Ly8xNzIuMjUuNzkuMTIyOjUwMDAvYmFja2VuZC8nXG5leHBvcnQgY29uc3Qgd2ViZGF2U2VydmljZVVybCA9ICdodHRwOi8vbG9jYWxob3N0OjUwMDEvJ1xuXG5leHBvcnQgY29uc3QgbGFuZ3VhZ2VzID0ge1xuICAgIFwiYXJcIjogXCLYp9mE2LnYsdio2YrYqVwiLFxuICAgIFwiZGVcIjogXCJEZXV0c2NoXCIsXG4gICAgXCJlblwiOiBcIkVuZ2xpc2hcIixcbiAgICBcImVzXCI6IFwiRXNwYcOxb2xcIixcbiAgICBcImZyXCI6IFwiRnJhbsOnYWlzXCIsXG4gICAgXCJoaVwiOiBcIuCkueCkv+CkqOCljeCkpuClgFwiLFxuICAgIFwiaXRcIjogXCJJdGFsaWFub1wiLFxuICAgIFwiamFcIjogXCLml6XmnKzoqp5cIixcbiAgICBcImtvXCI6IFwi7ZWc6rWt7Ja0XCIsXG4gICAgXCJubFwiOiBcIk5lZGVybGFuZHNcIixcbiAgICBcInB0XCI6IFwiUG9ydHVndcOqc1wiLFxuICAgIFwicnVcIjogXCLQoNGD0YHRgdC60LjQuVwiLFxuICAgIFwidHJcIjogXCJUw7xya8OnZVwiLFxuICAgIFwiemgtQ05cIjogXCLnroDkvZPkuK3mlodcIixcbiAgICBcInpoLVRXXCI6IFwi57mB6auU5Lit5paHXCJcbn07Il0sIm5hbWVzIjpbImJhY2tlbmRVcmwiLCJ3ZWJkYXZTZXJ2aWNlVXJsIiwibGFuZ3VhZ2VzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/page.js":
/*!**********************************!*\
  !*** ./src/app/[locale]/page.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalContext: () => (/* binding */ GlobalContext),\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   stringAvatar: () => (/* binding */ stringAvatar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AppBar/AppBar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/createTheme.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Toolbar/Toolbar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/InputAdornment/InputAdornment.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,CssBaseline,IconButton,InputAdornment,ListItemIcon,Menu,MenuItem,Select,TextField,Toolbar,Typography,createTheme,useMediaQuery!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Search */ \"(ssr)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_AccountCircle__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/AccountCircle */ \"(ssr)/./node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _mui_icons_material_GTranslate__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/GTranslate */ \"(ssr)/./node_modules/@mui/icons-material/esm/GTranslate.js\");\n/* harmony import */ var _mui_icons_material_ManageAccounts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/ManageAccounts */ \"(ssr)/./node_modules/@mui/icons-material/esm/ManageAccounts.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _src_app_locale_component_musicContainer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/app/[locale]/component/musicContainer */ \"(ssr)/./src/app/[locale]/component/musicContainer.js\");\n/* harmony import */ var _src_app_locale_component_audioPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/component/audioPlayer */ \"(ssr)/./src/app/[locale]/component/audioPlayer.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _toolpad_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @toolpad/core */ \"(ssr)/./node_modules/@toolpad/core/esm/useNotifications/NotificationsProvider.js\");\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(ssr)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(ssr)/./src/app/[locale]/config.js\");\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-cookie */ \"(ssr)/./node_modules/react-cookie/esm/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _marsidev_react_turnstile__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @marsidev/react-turnstile */ \"(ssr)/./node_modules/@marsidev/react-turnstile/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ GlobalContext,stringAvatar,default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst GlobalContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst StyledAppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(({ theme })=>({\n        backgroundColor: theme.palette.background.paper,\n        color: theme.palette.text.primary,\n        boxShadow: 'none',\n        borderBottom: `1px solid ${theme.palette.divider}`\n    }));\nconst theme = (0,_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    colorSchemes: {\n        dark: true\n    }\n});\nfunction stringToColor(string) {\n    let hash = 0;\n    let i;\n    /* eslint-disable no-bitwise */ for(i = 0; i < string.length; i += 1){\n        hash = string.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    let color = '#';\n    for(i = 0; i < 3; i += 1){\n        const value = hash >> i * 8 & 0xff;\n        color += `00${value.toString(16)}`.slice(-2);\n    }\n    /* eslint-enable no-bitwise */ return color;\n}\nfunction stringAvatar(name) {\n    return {\n        sx: {\n            bgcolor: name ? stringToColor(name) : null\n        },\n        children: name ? name[0] : null\n    };\n}\nfunction Home() {\n    const [cookie, setCookie] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_9__.useCookies)([\n        'username',\n        'NEXT_LOCALE'\n    ]);\n    const [webdavConfigs, setWebdavConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [songsData, setSongData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isAudioLoading, setAudioLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [playingSongData, setPlayingSongData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchType, setSearchType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('songs');\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [premium, setPremium] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [languageAnchorEl, setLanguageAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const languageOpen = Boolean(languageAnchorEl);\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_10__.useTranslations)('Home');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_10__.useLocale)();\n    const open = Boolean(anchorEl);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const isSmallScreen = (0,_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(theme.breakpoints.down('sm'));\n    const coverHeight = isSmallScreen ? '70px' : '80px';\n    const handleLanguageClick = (event)=>{\n        setLanguageAnchorEl(event.currentTarget);\n    };\n    const handleLanguageChange = (locale)=>{\n        setCookie('NEXT_LOCALE', locale, {\n            path: '/'\n        });\n        setLanguageAnchorEl(null);\n        router.push(\"/\" + locale);\n    };\n    const handleSearchSubmit = async (event)=>{\n        event.preventDefault();\n        if (searchValue.trim()) {\n            setLoading(true);\n            setToken(null);\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get('https://api.1music.cc/search', {\n                    withCredentials: true,\n                    params: {\n                        [searchType]: searchValue,\n                        token: token\n                    }\n                });\n                setSongData(response.data);\n            } catch (error) {\n                console.error('Search error:', error);\n            } finally{\n                setLoading(false);\n                ref.current?.reset();\n            }\n        }\n    };\n    const fetchRandomMusic = async ()=>{\n        try {\n            const response = await fetch(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_4__.backendUrl, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                throw new Error('获取随机歌曲失败');\n            }\n            const data = await response.json();\n            setSongData(data.music_list);\n        } catch (error) {\n            console.error('Error fetching random music:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"Home.useLayoutEffect\": ()=>{\n            if (false) {}\n            fetch(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_4__.backendUrl + 'get_user_profile/', {\n                credentials: 'include'\n            }).then({\n                \"Home.useLayoutEffect\": (response)=>{\n                    if (response.redirected) {}\n                    return response.json();\n                }\n            }[\"Home.useLayoutEffect\"]).then({\n                \"Home.useLayoutEffect\": (data)=>{\n                    setPremium(data.points && data.points > 100);\n                    setCookie('username', data.username || null);\n                    setWebdavConfigs(data.webdav_configs || []);\n                }\n            }[\"Home.useLayoutEffect\"]).catch({\n                \"Home.useLayoutEffect\": (err)=>{\n                    setPremium(false);\n                    console.log(err);\n                }\n            }[\"Home.useLayoutEffect\"]);\n            fetchRandomMusic();\n        }\n    }[\"Home.useLayoutEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (premium !== true) {\n                const script_ = document.createElement(\"script\");\n                script_.setAttribute(\"data-cfasync\", \"false\");\n                script_.innerHTML = ``;\n                document.head.appendChild(script_);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        premium\n    ]);\n    const handleAvatarClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toolpad_core__WEBPACK_IMPORTED_MODULE_14__.NotificationsProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlobalContext.Provider, {\n                value: {\n                    setPlayingSongData,\n                    setAudioLoading,\n                    webdavConfigs,\n                    premium\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    theme: theme,\n                    noSsr: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                            lineNumber: 221,\n                            columnNumber: 56\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StyledAppBar, {\n                            position: \"static\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        variant: \"h6\",\n                                        onClick: ()=>fetchRandomMusic(),\n                                        component: \"div\",\n                                        sx: {\n                                            flexGrow: 0.1,\n                                            fontWeight: 'bold',\n                                            fontFamily: \"HarmonyOS Sans,system-ui\",\n                                            letterSpacing: -0.5\n                                        },\n                                        children: \"1Music.cc\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                        lineNumber: 224,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        sx: {\n                                            mx: 2,\n                                            display: 'flex',\n                                            gap: '8px',\n                                            flexGrow: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSearchSubmit,\n                                                style: {\n                                                    alignItems: 'center',\n                                                    flexGrow: 1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    fullWidth: true,\n                                                    disabled: !token,\n                                                    placeholder: \"Search...\",\n                                                    value: searchValue,\n                                                    onChange: (e)=>setSearchValue(e.target.value),\n                                                    InputProps: {\n                                                        startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            position: \"start\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 53\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 49\n                                                        }, void 0)\n                                                    },\n                                                    variant: \"outlined\",\n                                                    size: \"small\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                lineNumber: 234,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                value: searchType,\n                                                onChange: (e)=>setSearchType(e.target.value),\n                                                variant: \"outlined\",\n                                                size: \"small\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        value: \"songs\",\n                                                        children: \"Songs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        value: \"artists\",\n                                                        children: \"Artists\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                        lineNumber: 233,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        color: \"inherit\",\n                                        onClick: handleAvatarClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            ...stringAvatar(cookie['username'] ? cookie['username'] : null)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                        lineNumber: 263,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        anchorEl: anchorEl,\n                                        open: open,\n                                        onClose: handleClose,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                onClick: ()=>router.replace(cookie['username'] ? '/user' : '/login'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_AccountCircle__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 51\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    cookie['username'] ? cookie['username'] : t(\"login\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                lineNumber: 267,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                onClick: handleLanguageClick,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_GTranslate__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 51\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    t(\"setLanguage\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                lineNumber: 271,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                anchorEl: languageAnchorEl,\n                                                open: languageOpen,\n                                                onClose: ()=>setLanguageAnchorEl(null),\n                                                children: Object.entries(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_4__.languages).map(([langCode, langName])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        onClick: ()=>handleLanguageChange(langCode),\n                                                        children: langName\n                                                    }, langCode, false, {\n                                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 41\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                lineNumber: 275,\n                                                columnNumber: 33\n                                            }, this),\n                                            cookie['username'] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                onClick: ()=>router.replace(cookie['username'] ? '/user' : '/login'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ManageAccounts__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 51\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    t(\"manageUser\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                                lineNumber: 282,\n                                                columnNumber: 56\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                        lineNumber: 266,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                lineNumber: 223,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                            lineNumber: 222,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_app_locale_component_musicContainer__WEBPACK_IMPORTED_MODULE_2__.MusicCardContainer, {\n                            songs: songsData,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                            lineNumber: 289,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_marsidev_react_turnstile__WEBPACK_IMPORTED_MODULE_32__.Turnstile, {\n                            ref: ref,\n                            siteKey: \"0x4AAAAAABBxNT2ftEWvsRrE\",\n                            size: \"invisible\",\n                            onSuccess: (token)=>setToken(token)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                            lineNumber: 290,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            sx: {\n                                mt: 0.5,\n                                textAlign: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: \"Contact me: <EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                    lineNumber: 297,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: \"\\xa9 2025 1Music.cc All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                                    lineNumber: 300,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                            lineNumber: 296,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_CssBaseline_IconButton_InputAdornment_ListItemIcon_Menu_MenuItem_Select_TextField_Toolbar_Typography_createTheme_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            sx: {\n                                mt: 1,\n                                height: coverHeight\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                            lineNumber: 304,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_app_locale_component_audioPlayer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            coverHeight: coverHeight,\n                            songData: playingSongData\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                            lineNumber: 305,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                    lineNumber: 221,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n                lineNumber: 220,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n            lineNumber: 219,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\",\n        lineNumber: 218,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL3BhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNrRjtBQVUzRDtBQUM2QjtBQUNjO0FBQ047QUFDUTtBQUV0QjtBQUNPO0FBRTBCO0FBQ1o7QUFDekM7QUFDMEI7QUFDWjtBQUN3QjtBQUN4QjtBQUNFO0FBQ1c7QUFDRDtBQUc3QyxNQUFNc0MsOEJBQWdCdEMsb0RBQWFBLENBQUMsQ0FBQyxHQUFHO0FBRS9DLE1BQU11QyxlQUFlZixnRUFBTUEsQ0FBQ2xCLDROQUFNQSxFQUFFLENBQUMsRUFBRWtDLEtBQUssRUFBRSxHQUFNO1FBQ2hEQyxpQkFBaUJELE1BQU1FLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDQyxLQUFLO1FBQy9DQyxPQUFPTCxNQUFNRSxPQUFPLENBQUNJLElBQUksQ0FBQ0MsT0FBTztRQUNqQ0MsV0FBVztRQUNYQyxjQUFjLENBQUMsVUFBVSxFQUFFVCxNQUFNRSxPQUFPLENBQUNRLE9BQU8sRUFBRTtJQUN0RDtBQUVBLE1BQU1WLFFBQVF2QixnT0FBV0EsQ0FBQztJQUN0QmtDLGNBQWM7UUFDVkMsTUFBTTtJQUNWO0FBQ0o7QUFFQSxTQUFTQyxjQUFjQyxNQUFNO0lBQ3pCLElBQUlDLE9BQU87SUFDWCxJQUFJQztJQUVKLDZCQUE2QixHQUM3QixJQUFLQSxJQUFJLEdBQUdBLElBQUlGLE9BQU9HLE1BQU0sRUFBRUQsS0FBSyxFQUFHO1FBQ25DRCxPQUFPRCxPQUFPSSxVQUFVLENBQUNGLEtBQU0sRUFBQ0QsUUFBUSxLQUFLQSxJQUFHO0lBQ3BEO0lBRUEsSUFBSVYsUUFBUTtJQUVaLElBQUtXLElBQUksR0FBR0EsSUFBSSxHQUFHQSxLQUFLLEVBQUc7UUFDdkIsTUFBTUcsUUFBUSxRQUFVSCxJQUFJLElBQU07UUFDbENYLFNBQVMsQ0FBQyxFQUFFLEVBQUVjLE1BQU1DLFFBQVEsQ0FBQyxLQUFLLENBQUNDLEtBQUssQ0FBQyxDQUFDO0lBQzlDO0lBQ0EsNEJBQTRCLEdBRTVCLE9BQU9oQjtBQUNYO0FBRU8sU0FBU2lCLGFBQWFDLElBQUk7SUFDN0IsT0FBTztRQUNIQyxJQUFJO1lBQ0FDLFNBQVVGLE9BQU9WLGNBQWNVLFFBQVE7UUFDM0M7UUFDQUcsVUFBVUgsT0FBT0EsSUFBSSxDQUFDLEVBQUUsR0FBRztJQUMvQjtBQUNKO0FBRWUsU0FBU0k7SUFDcEIsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdwQyx3REFBVUEsQ0FBQztRQUFDO1FBQVc7S0FBYztJQUNqRSxNQUFNLENBQUNxQyxlQUFlQyxpQkFBaUIsR0FBR3BFLCtDQUFRQSxDQUFDLEVBQUU7SUFDckQsTUFBTSxDQUFDcUUsYUFBYUMsZUFBZSxHQUFHdEUsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDdUUsU0FBU0MsV0FBVyxHQUFHeEUsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDeUUsV0FBV0MsWUFBWSxHQUFHMUUsK0NBQVFBLENBQUMsRUFBRTtJQUM1QyxNQUFNLENBQUMyRSxnQkFBZ0JDLGdCQUFnQixHQUFHNUUsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDNkUsaUJBQWlCQyxtQkFBbUIsR0FBRzlFLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQytFLFlBQVlDLGNBQWMsR0FBR2hGLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2lGLFVBQVVDLFlBQVksR0FBR2xGLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ21GLFNBQVFDLFdBQVcsR0FBR3BGLCtDQUFRQSxDQUFDO0lBQ3RDLE1BQU0sQ0FBQ3FGLE9BQU9DLFNBQVMsR0FBR3RGLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ3VGLGtCQUFrQkMsb0JBQW9CLEdBQUd4RiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNeUYsZUFBZUMsUUFBUUg7SUFDN0IsTUFBTUksSUFBSTFELDJEQUFlQSxDQUFDO0lBQzFCLE1BQU0yRCxTQUFTN0QsMERBQVNBO0lBQ3hCLE1BQU04RCxTQUFTN0QscURBQVNBO0lBQ3hCLE1BQU04RCxPQUFPSixRQUFRVDtJQUNyQixNQUFNYyxNQUFNOUYsNkNBQU1BO0lBQ2xCLE1BQU0rRixnQkFBZ0JoRixpT0FBYUEsQ0FBQ3FCLE1BQU00RCxXQUFXLENBQUNDLElBQUksQ0FBQztJQUMzRCxNQUFNQyxjQUFjSCxnQkFBZ0IsU0FBUztJQUU3QyxNQUFNSSxzQkFBc0IsQ0FBQ0M7UUFDekJiLG9CQUFvQmEsTUFBTUMsYUFBYTtJQUMzQztJQUVBLE1BQU1DLHVCQUF1QixDQUFDVjtRQUMxQjNCLFVBQVUsZUFBZTJCLFFBQVE7WUFBRVcsTUFBTTtRQUFJO1FBQzdDaEIsb0JBQW9CO1FBQ3BCSSxPQUFPYSxJQUFJLENBQUMsTUFBTVo7SUFDdEI7SUFFQSxNQUFNYSxxQkFBcUIsT0FBT0w7UUFDOUJBLE1BQU1NLGNBQWM7UUFDcEIsSUFBSXRDLFlBQVl1QyxJQUFJLElBQUk7WUFDcEJwQyxXQUFXO1lBQ1hjLFNBQVM7WUFDVCxJQUFJO2dCQUNBLE1BQU11QixXQUFXLE1BQU1wRiw4Q0FBS0EsQ0FBQ3FGLEdBQUcsQ0FBQyxnQ0FBZ0M7b0JBQzdEQyxpQkFBaUI7b0JBQ2pCQyxRQUFRO3dCQUFFLENBQUNqQyxXQUFXLEVBQUVWO3dCQUFhZ0IsT0FBT0E7b0JBQU07Z0JBQ3REO2dCQUNBWCxZQUFZbUMsU0FBU0ksSUFBSTtZQUM3QixFQUFFLE9BQU9DLE9BQU87Z0JBQ1pDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1lBQ25DLFNBQVU7Z0JBQ04xQyxXQUFXO2dCQUNYdUIsSUFBSXFCLE9BQU8sRUFBRUM7WUFDakI7UUFDSjtJQUNKO0lBRUEsTUFBTUMsbUJBQW1CO1FBQ3JCLElBQUk7WUFDQSxNQUFNVCxXQUFXLE1BQU1VLE1BQU0zRiw4REFBVUEsRUFBRTtnQkFDckM0RixRQUFRO2dCQUNSQyxTQUFTO29CQUNMLGdCQUFnQjtnQkFDcEI7Z0JBQ0FDLGFBQWE7WUFDakI7WUFFQSxJQUFJLENBQUNiLFNBQVNjLEVBQUUsRUFBRTtnQkFDZCxNQUFNLElBQUlDLE1BQU07WUFDcEI7WUFFQSxNQUFNWCxPQUFPLE1BQU1KLFNBQVNnQixJQUFJO1lBQ2hDbkQsWUFBWXVDLEtBQUthLFVBQVU7UUFDL0IsRUFBRSxPQUFPWixPQUFPO1lBQ1pDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQ2xEO0lBQ0o7SUFFQW5ILHNEQUFlQTtnQ0FBQztZQUNaLElBQUksS0FBNkIsRUFBRSxFQXlCbEM7WUFFRHdILE1BQU0zRiw4REFBVUEsR0FBRyxxQkFBb0I7Z0JBQ25DOEYsYUFBWTtZQUNoQixHQUNLaUIsSUFBSTt3Q0FBQzlCLENBQUFBO29CQUNGLElBQUlBLFNBQVMrQixVQUFVLEVBQUUsQ0FDekI7b0JBQ0EsT0FBTy9CLFNBQVNnQixJQUFJO2dCQUN4Qjt1Q0FDQ2MsSUFBSTt3Q0FBQzFCLENBQUFBO29CQUNGN0IsV0FBVzZCLEtBQUs0QixNQUFNLElBQUk1QixLQUFLNEIsTUFBTSxHQUFHO29CQUN4QzNFLFVBQVUsWUFBVytDLEtBQUs2QixRQUFRLElBQUk7b0JBQ3RDMUUsaUJBQWlCNkMsS0FBSzhCLGNBQWMsSUFBSSxFQUFFO2dCQUM5Qzt1Q0FDQ0MsS0FBSzt3Q0FBQ0MsQ0FBQUE7b0JBQ0g3RCxXQUFXO29CQUNYK0IsUUFBUStCLEdBQUcsQ0FBQ0Q7Z0JBQ2hCOztZQUVKM0I7UUFFSjsrQkFBRyxFQUFFO0lBRUx4SCxnREFBU0E7MEJBQUM7WUFDTixJQUFJcUYsWUFBWSxNQUFNO2dCQUNsQixNQUFNZ0UsVUFBVUMsU0FBU0MsYUFBYSxDQUFDO2dCQUN2Q0YsUUFBUUcsWUFBWSxDQUFDLGdCQUFnQjtnQkFDckNILFFBQVFJLFNBQVMsR0FBRyxFQUFFO2dCQUN0QkgsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNOO1lBQzlCO1FBQ0o7eUJBQUc7UUFBQ2hFO0tBQVE7SUFFWixNQUFNdUUsb0JBQW9CLENBQUNyRDtRQUN2Qm5CLFlBQVltQixNQUFNQyxhQUFhO0lBQ25DO0lBRUEsTUFBTXFELGNBQWM7UUFDaEJ6RSxZQUFZO0lBQ2hCO0lBRUEscUJBQ0ksOERBQUN2RCw0REFBS0E7a0JBQ0YsNEVBQUNELGlFQUFxQkE7c0JBQ2xCLDRFQUFDUyxjQUFjeUgsUUFBUTtnQkFBQ3BHLE9BQU87b0JBQUNzQjtvQkFBb0JGO29CQUFpQlQ7b0JBQWVnQjtnQkFBUTswQkFDeEYsNEVBQUM3RCw2REFBYUE7b0JBQUNlLE9BQU9BO29CQUFPd0gsS0FBSzs7c0NBQUMsOERBQUM5SSw2TkFBV0E7Ozs7O3NDQUMvQyw4REFBQ3FCOzRCQUFhMEgsVUFBUztzQ0FDbkIsNEVBQUMxSiw2TkFBT0E7O2tEQUNKLDhEQUFDQyw2TkFBVUE7d0NBQUMwSixTQUFRO3dDQUFLQyxTQUFTLElBQU0xQzt3Q0FBb0IyQyxXQUFVO3dDQUFNcEcsSUFBSTs0Q0FDNUVxRyxVQUFVOzRDQUNWQyxZQUFZOzRDQUNaQyxZQUFZOzRDQUNaQyxlQUFlLENBQUM7d0NBQ3BCO2tEQUFHOzs7Ozs7a0RBSUgsOERBQUNuSyw2TkFBR0E7d0NBQUMyRCxJQUFJOzRDQUFFeUcsSUFBSTs0Q0FBR0MsU0FBUzs0Q0FBUUMsS0FBSzs0Q0FBT04sVUFBVTt3Q0FBRTs7MERBQ3ZELDhEQUFDTztnREFBS0MsVUFBVWhFO2dEQUFvQmlFLE9BQU87b0RBQUVDLFlBQVk7b0RBQVVWLFVBQVU7Z0RBQUU7MERBQzNFLDRFQUFDM0osNk5BQVNBO29EQUNOc0ssU0FBUztvREFDVEMsVUFBVSxDQUFDekY7b0RBQ1gwRixhQUFZO29EQUNadkgsT0FBT2E7b0RBQ1AyRyxVQUFVLENBQUNDLElBQU0zRyxlQUFlMkcsRUFBRUMsTUFBTSxDQUFDMUgsS0FBSztvREFDOUMySCxZQUFZO3dEQUNSQyw4QkFDSSw4REFBQzVLLDZOQUFjQTs0REFBQ3NKLFVBQVM7c0VBQ3JCLDRFQUFDN0ksbUVBQVVBOzs7Ozs7Ozs7O29EQUd2QjtvREFDQThJLFNBQVE7b0RBQ1JzQixNQUFLOzs7Ozs7Ozs7OzswREFHYiw4REFBQzNLLDZOQUFNQTtnREFDSDhDLE9BQU91QjtnREFDUGlHLFVBQVUsQ0FBQ0MsSUFBTWpHLGNBQWNpRyxFQUFFQyxNQUFNLENBQUMxSCxLQUFLO2dEQUM3Q3VHLFNBQVE7Z0RBQ1JzQixNQUFLOztrRUFFTCw4REFBQzFLLDZOQUFRQTt3REFBQzZDLE9BQU07a0VBQVE7Ozs7OztrRUFDeEIsOERBQUM3Qyw2TkFBUUE7d0RBQUM2QyxPQUFNO2tFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSWxDLDhEQUFDbEQsNk5BQVVBO3dDQUFDb0MsT0FBTTt3Q0FBVXNILFNBQVNOO2tEQUNqQyw0RUFBQ2pKLDZOQUFNQTs0Q0FBRSxHQUFHa0QsYUFBYU0sTUFBTSxDQUFDLFdBQVcsR0FBR0EsTUFBTSxDQUFDLFdBQVcsR0FBRyxLQUFLOzs7Ozs7Ozs7OztrREFFNUUsOERBQUNwRCw2TkFBSUE7d0NBQUNvRSxVQUFVQTt3Q0FBVWEsTUFBTUE7d0NBQU13RixTQUFTM0I7OzBEQUMzQyw4REFBQ2hKLDZOQUFRQTtnREFBQ3FKLFNBQVMsSUFBTXBFLE9BQU84QyxPQUFPLENBQUN6RSxNQUFNLENBQUMsV0FBVyxHQUFHLFVBQVU7O2tFQUNuRSw4REFBQ3JELDZOQUFZQTtrRUFBQyw0RUFBQ00sMEVBQWlCQTs7Ozs7Ozs7OztvREFDL0IrQyxNQUFNLENBQUMsV0FBVyxHQUFHQSxNQUFNLENBQUMsV0FBVyxHQUFHMEIsRUFBRTs7Ozs7OzswREFFakQsOERBQUNoRiw2TkFBUUE7Z0RBQUNxSixTQUFTNUQ7O2tFQUNmLDhEQUFDeEYsNk5BQVlBO2tFQUFDLDRFQUFDTyx1RUFBY0E7Ozs7Ozs7Ozs7b0RBQzVCd0UsRUFBRTs7Ozs7OzswREFFUCw4REFBQzlFLDZOQUFJQTtnREFBQ29FLFVBQVVNO2dEQUFrQk8sTUFBTUw7Z0RBQWM2RixTQUFTLElBQU05RixvQkFBb0I7MERBQ3BGOEMsT0FBT2lELE9BQU8sQ0FBQzFKLDZEQUFTQSxFQUFFMkosR0FBRyxDQUFDLENBQUMsQ0FBQ0MsVUFBVUMsU0FBUyxpQkFDaEQsOERBQUMvSyw2TkFBUUE7d0RBQWdCcUosU0FBUyxJQUFNekQscUJBQXFCa0Y7a0VBQ3hEQzt1REFEVUQ7Ozs7Ozs7Ozs7NENBS3RCeEgsTUFBTSxDQUFDLFdBQVcsa0JBQUksOERBQUN0RCw2TkFBUUE7Z0RBQUNxSixTQUFTLElBQU1wRSxPQUFPOEMsT0FBTyxDQUFDekUsTUFBTSxDQUFDLFdBQVcsR0FBRyxVQUFVOztrRUFDMUYsOERBQUNyRCw2TkFBWUE7a0VBQUMsNEVBQUNRLDJFQUFrQkE7Ozs7Ozs7Ozs7b0RBQ2hDdUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtuQiw4REFBQ3BFLHdGQUFrQkE7NEJBQUNvSyxPQUFPbEg7NEJBQVdGLFNBQVNBOzs7Ozs7c0NBQy9DLDhEQUFDckMsaUVBQVNBOzRCQUNONkQsS0FBS0E7NEJBQ0w2RixTQUFROzRCQUNSUCxNQUFLOzRCQUNMUSxXQUFXLENBQUN4RyxRQUFVQyxTQUFTRDs7Ozs7O3NDQUVuQyw4REFBQ25GLDZOQUFHQTs0QkFBQzJELElBQUk7Z0NBQUVpSSxJQUFJO2dDQUFLQyxXQUFXOzRCQUFTOzs4Q0FDcEMsOERBQUMxTCw2TkFBVUE7b0NBQUMwSixTQUFRO29DQUFRckgsT0FBTTtvQ0FBZ0JtQixJQUFJO3dDQUFFbUksSUFBSTtvQ0FBRTs4Q0FBRzs7Ozs7OzhDQUdqRSw4REFBQzNMLDZOQUFVQTtvQ0FBQzBKLFNBQVE7b0NBQVFySCxPQUFNOzhDQUFnQjs7Ozs7Ozs7Ozs7O3NDQUl0RCw4REFBQ3hDLDZOQUFHQTs0QkFBQzJELElBQUk7Z0NBQUVpSSxJQUFHO2dDQUFFRyxRQUFROUY7NEJBQVk7Ozs7OztzQ0FDcEMsOERBQUMzRSw2RUFBV0E7NEJBQUMyRSxhQUFhQTs0QkFBYStGLFVBQVVySDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXJFIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcc3JjXFxhcHBcXFtsb2NhbGVdXFxwYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHtjcmVhdGVDb250ZXh0LCB1c2VFZmZlY3QsIHVzZUxheW91dEVmZmVjdCwgdXNlU3RhdGUsIHVzZVJlZn0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHtcbiAgICBCb3gsXG4gICAgQXBwQmFyLFxuICAgIFRvb2xiYXIsXG4gICAgVHlwb2dyYXBoeSxcbiAgICBJY29uQnV0dG9uLFxuICAgIFRleHRGaWVsZCxcbiAgICBJbnB1dEFkb3JubWVudCxcbiAgICBBdmF0YXIsIFNlbGVjdCwgTWVudUl0ZW0sIExpc3RJdGVtSWNvbiwgTWVudSwgY3JlYXRlVGhlbWUsIENzc0Jhc2VsaW5lLCB1c2VNZWRpYVF1ZXJ5XG59IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xuaW1wb3J0IFNlYXJjaEljb24gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbC9TZWFyY2gnO1xuaW1wb3J0IEFjY291bnRDaXJjbGVJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvQWNjb3VudENpcmNsZSc7XG5pbXBvcnQgR1RyYW5zbGF0ZUljb24gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbC9HVHJhbnNsYXRlJztcbmltcG9ydCBNYW5hZ2VBY2NvdW50c0ljb24gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbC9NYW5hZ2VBY2NvdW50cyc7XG5cbmltcG9ydCB7IHN0eWxlZCB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwvc3R5bGVzJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICdAbXVpL21hdGVyaWFsL3N0eWxlcyc7XG5cbmltcG9ydCB7TXVzaWNDYXJkQ29udGFpbmVyfSBmcm9tIFwiQC9zcmMvYXBwL1tsb2NhbGVdL2NvbXBvbmVudC9tdXNpY0NvbnRhaW5lclwiO1xuaW1wb3J0IEF1ZGlvUGxheWVyIGZyb20gXCJAL3NyYy9hcHAvW2xvY2FsZV0vY29tcG9uZW50L2F1ZGlvUGxheWVyXCI7XG5pbXBvcnQgYXhpb3MgZnJvbSBcImF4aW9zXCI7XG5pbXBvcnQge05vdGlmaWNhdGlvbnNQcm92aWRlcn0gZnJvbSBcIkB0b29scGFkL2NvcmVcIjtcbmltcG9ydCBOb1NzciBmcm9tIFwiQG11aS9tYXRlcmlhbC9Ob1NzclwiO1xuaW1wb3J0IHtiYWNrZW5kVXJsLCBsYW5ndWFnZXN9IGZyb20gXCJAL3NyYy9hcHAvW2xvY2FsZV0vY29uZmlnXCI7XG5pbXBvcnQge3VzZUNvb2tpZXN9IGZyb20gXCJyZWFjdC1jb29raWVcIjtcbmltcG9ydCB7dXNlUm91dGVyfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQge3VzZUxvY2FsZSwgdXNlVHJhbnNsYXRpb25zfSBmcm9tIFwibmV4dC1pbnRsXCI7XG5pbXBvcnQge1R1cm5zdGlsZX0gZnJvbSBcIkBtYXJzaWRldi9yZWFjdC10dXJuc3RpbGVcIjtcblxuXG5leHBvcnQgY29uc3QgR2xvYmFsQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoe30pO1xuXG5jb25zdCBTdHlsZWRBcHBCYXIgPSBzdHlsZWQoQXBwQmFyKSgoeyB0aGVtZSB9KSA9PiAoe1xuICAgIGJhY2tncm91bmRDb2xvcjogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnBhcGVyLFxuICAgIGNvbG9yOiB0aGVtZS5wYWxldHRlLnRleHQucHJpbWFyeSxcbiAgICBib3hTaGFkb3c6ICdub25lJyxcbiAgICBib3JkZXJCb3R0b206IGAxcHggc29saWQgJHt0aGVtZS5wYWxldHRlLmRpdmlkZXJ9YCxcbn0pKTtcblxuY29uc3QgdGhlbWUgPSBjcmVhdGVUaGVtZSh7XG4gICAgY29sb3JTY2hlbWVzOiB7XG4gICAgICAgIGRhcms6IHRydWUsXG4gICAgfSxcbn0pO1xuXG5mdW5jdGlvbiBzdHJpbmdUb0NvbG9yKHN0cmluZykge1xuICAgIGxldCBoYXNoID0gMDtcbiAgICBsZXQgaTtcblxuICAgIC8qIGVzbGludC1kaXNhYmxlIG5vLWJpdHdpc2UgKi9cbiAgICBmb3IgKGkgPSAwOyBpIDwgc3RyaW5nLmxlbmd0aDsgaSArPSAxKSB7XG4gICAgICAgIGhhc2ggPSBzdHJpbmcuY2hhckNvZGVBdChpKSArICgoaGFzaCA8PCA1KSAtIGhhc2gpO1xuICAgIH1cblxuICAgIGxldCBjb2xvciA9ICcjJztcblxuICAgIGZvciAoaSA9IDA7IGkgPCAzOyBpICs9IDEpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSAoaGFzaCA+PiAoaSAqIDgpKSAmIDB4ZmY7XG4gICAgICAgIGNvbG9yICs9IGAwMCR7dmFsdWUudG9TdHJpbmcoMTYpfWAuc2xpY2UoLTIpO1xuICAgIH1cbiAgICAvKiBlc2xpbnQtZW5hYmxlIG5vLWJpdHdpc2UgKi9cblxuICAgIHJldHVybiBjb2xvcjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ0F2YXRhcihuYW1lKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc3g6IHtcbiAgICAgICAgICAgIGJnY29sb3I6ICBuYW1lID8gc3RyaW5nVG9Db2xvcihuYW1lKSA6IG51bGwsXG4gICAgICAgIH0sXG4gICAgICAgIGNoaWxkcmVuOiBuYW1lID8gbmFtZVswXSA6IG51bGwsXG4gICAgfTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgICBjb25zdCBbY29va2llLCBzZXRDb29raWVdID0gdXNlQ29va2llcyhbJ3VzZXJuYW1lJywnTkVYVF9MT0NBTEUnXSk7XG4gICAgY29uc3QgW3dlYmRhdkNvbmZpZ3MsIHNldFdlYmRhdkNvbmZpZ3NdID0gdXNlU3RhdGUoW10pO1xuICAgIGNvbnN0IFtzZWFyY2hWYWx1ZSwgc2V0U2VhcmNoVmFsdWVdID0gdXNlU3RhdGUoJycpO1xuICAgIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgICBjb25zdCBbc29uZ3NEYXRhLCBzZXRTb25nRGF0YV0gPSB1c2VTdGF0ZShbXSk7XG4gICAgY29uc3QgW2lzQXVkaW9Mb2FkaW5nLCBzZXRBdWRpb0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICAgIGNvbnN0IFtwbGF5aW5nU29uZ0RhdGEsIHNldFBsYXlpbmdTb25nRGF0YV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgICBjb25zdCBbc2VhcmNoVHlwZSwgc2V0U2VhcmNoVHlwZV0gPSB1c2VTdGF0ZSgnc29uZ3MnKTtcbiAgICBjb25zdCBbYW5jaG9yRWwsIHNldEFuY2hvckVsXSA9IHVzZVN0YXRlKG51bGwpO1xuICAgIGNvbnN0IFtwcmVtaXVtLHNldFByZW1pdW1dID0gdXNlU3RhdGUodHJ1ZSlcbiAgICBjb25zdCBbdG9rZW4sIHNldFRva2VuXSA9IHVzZVN0YXRlKG51bGwpXG4gICAgY29uc3QgW2xhbmd1YWdlQW5jaG9yRWwsIHNldExhbmd1YWdlQW5jaG9yRWxdID0gdXNlU3RhdGUobnVsbCk7XG4gICAgY29uc3QgbGFuZ3VhZ2VPcGVuID0gQm9vbGVhbihsYW5ndWFnZUFuY2hvckVsKTtcbiAgICBjb25zdCB0ID0gdXNlVHJhbnNsYXRpb25zKCdIb21lJylcbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgICBjb25zdCBsb2NhbGUgPSB1c2VMb2NhbGUoKVxuICAgIGNvbnN0IG9wZW4gPSBCb29sZWFuKGFuY2hvckVsKTtcbiAgICBjb25zdCByZWYgPSB1c2VSZWYoKVxuICAgIGNvbnN0IGlzU21hbGxTY3JlZW4gPSB1c2VNZWRpYVF1ZXJ5KHRoZW1lLmJyZWFrcG9pbnRzLmRvd24oJ3NtJykpO1xuICAgIGNvbnN0IGNvdmVySGVpZ2h0ID0gaXNTbWFsbFNjcmVlbiA/ICc3MHB4JyA6ICc4MHB4J1xuXG4gICAgY29uc3QgaGFuZGxlTGFuZ3VhZ2VDbGljayA9IChldmVudCkgPT4ge1xuICAgICAgICBzZXRMYW5ndWFnZUFuY2hvckVsKGV2ZW50LmN1cnJlbnRUYXJnZXQpO1xuICAgIH07XG5cbiAgICBjb25zdCBoYW5kbGVMYW5ndWFnZUNoYW5nZSA9IChsb2NhbGUpID0+IHtcbiAgICAgICAgc2V0Q29va2llKCdORVhUX0xPQ0FMRScsIGxvY2FsZSwgeyBwYXRoOiAnLycgfSk7XG4gICAgICAgIHNldExhbmd1YWdlQW5jaG9yRWwobnVsbCk7XG4gICAgICAgIHJvdXRlci5wdXNoKFwiL1wiICsgbG9jYWxlKVxuICAgIH07XG5cbiAgICBjb25zdCBoYW5kbGVTZWFyY2hTdWJtaXQgPSBhc3luYyAoZXZlbnQpID0+IHtcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgaWYgKHNlYXJjaFZhbHVlLnRyaW0oKSkge1xuICAgICAgICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgICAgICAgIHNldFRva2VuKG51bGwpXG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KCdodHRwczovL2FwaS4xbXVzaWMuY2Mvc2VhcmNoJywge1xuICAgICAgICAgICAgICAgICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgIHBhcmFtczogeyBbc2VhcmNoVHlwZV06IHNlYXJjaFZhbHVlLCB0b2tlbjogdG9rZW4gfSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBzZXRTb25nRGF0YShyZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignU2VhcmNoIGVycm9yOicsIGVycm9yKTtcbiAgICAgICAgICAgIH0gZmluYWxseSB7XG4gICAgICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgICAgICAgICAgcmVmLmN1cnJlbnQ/LnJlc2V0KClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH07XG5cbiAgICBjb25zdCBmZXRjaFJhbmRvbU11c2ljID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChiYWNrZW5kVXJsLCB7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+iOt+WPlumaj+acuuatjOabsuWksei0pScpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgc2V0U29uZ0RhdGEoZGF0YS5tdXNpY19saXN0KTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHJhbmRvbSBtdXNpYzonLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICB9O1xuXG4gICAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgIGxldCB1c2VyTGFuZyA9IGNvb2tpZVsnTkVYVF9MT0NBTEUnXSB8fCBuYXZpZ2F0b3IubGFuZ3VhZ2UgfHwgbmF2aWdhdG9yLnVzZXJMYW5ndWFnZTtcblxuICAgICAgICAgICAgLy8g5aSE55CG57mB5L2T5Lit5paHXG4gICAgICAgICAgICBpZiAodXNlckxhbmcgPT09IFwiemgtSEtcIiB8fCB1c2VyTGFuZyA9PT0gXCJ6aC1NT1wiKSB7XG4gICAgICAgICAgICAgICAgdXNlckxhbmcgPSBcInpoLVRXXCI7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOWPquiOt+WPliBg6K+t6KiA5Luj56CBYO+8iOavlOWmgiBgZW4tVVNgID0+IGBlbmDvvIlcbiAgICAgICAgICAgIGxldCBsYW5nUHJlZml4ID0gdXNlckxhbmcuc3BsaXQoXCItXCIpWzBdOyAvLyDmj5Dlj5bliY3nvIDvvIzlpoIgYGVuYCwgYHpoYCwgYGZyYFxuXG4gICAgICAgICAgICAvLyDlrprkuYnmlK/mjIHnmoTor63oqIBcbiAgICAgICAgICAgIGNvbnN0IHN1cHBvcnRlZExvY2FsZXMgPSBPYmplY3Qua2V5cyhsYW5ndWFnZXMpO1xuXG4gICAgICAgICAgICAvLyDlpoLmnpwgYHVzZXJMYW5nYCDnm7TmjqXljLnphY3mlK/mjIHnmoTor63oqIDvvIzliJnkvb/nlKjlrozmlbTnmoQgYHVzZXJMYW5nYFxuICAgICAgICAgICAgbGV0IGRldGVjdGVkTG9jYWxlID0gc3VwcG9ydGVkTG9jYWxlcy5pbmNsdWRlcyh1c2VyTGFuZylcbiAgICAgICAgICAgICAgICA/IHVzZXJMYW5nXG4gICAgICAgICAgICAgICAgOiBzdXBwb3J0ZWRMb2NhbGVzLmluY2x1ZGVzKGxhbmdQcmVmaXgpXG4gICAgICAgICAgICAgICAgICAgID8gbGFuZ1ByZWZpeCAgLy8g5aaC5p6c5a6M5pW05Yy56YWN5aSx6LSl77yM5L2/55So5YmN57yA5Yy56YWNXG4gICAgICAgICAgICAgICAgICAgIDogXCJlblwiOyAvLyDpu5jorqTlm57pgIDliLAgYHpoX0NOYFxuXG4gICAgICAgICAgICAvLyDlj6rmnInlvZPmo4DmtYvliLDnmoTor63oqIDkuI7lvZPliY0gYGxvY2FsZWAg5LiN5ZCM5pe25omN5YiH5o2iXG4gICAgICAgICAgICBpZiAobG9jYWxlICE9PSBkZXRlY3RlZExvY2FsZSkge1xuICAgICAgICAgICAgICAgIHJvdXRlci5yZXBsYWNlKGAvJHtkZXRlY3RlZExvY2FsZX1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGZldGNoKGJhY2tlbmRVcmwgKyAnZ2V0X3VzZXJfcHJvZmlsZS8nLHtcbiAgICAgICAgICAgIGNyZWRlbnRpYWxzOidpbmNsdWRlJ1xuICAgICAgICB9KVxuICAgICAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5yZWRpcmVjdGVkKSB7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiByZXNwb25zZS5qc29uKClcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAudGhlbihkYXRhID0+IHtcbiAgICAgICAgICAgICAgICBzZXRQcmVtaXVtKGRhdGEucG9pbnRzICYmIGRhdGEucG9pbnRzID4gMTAwKVxuICAgICAgICAgICAgICAgIHNldENvb2tpZSgndXNlcm5hbWUnLGRhdGEudXNlcm5hbWUgfHwgbnVsbClcbiAgICAgICAgICAgICAgICBzZXRXZWJkYXZDb25maWdzKGRhdGEud2ViZGF2X2NvbmZpZ3MgfHwgW10pO1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC5jYXRjaChlcnIgPT4ge1xuICAgICAgICAgICAgICAgIHNldFByZW1pdW0oZmFsc2UpXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coZXJyKVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgZmV0Y2hSYW5kb21NdXNpYygpO1xuXG4gICAgfSwgW10pO1xuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKHByZW1pdW0gIT09IHRydWUpIHtcbiAgICAgICAgICAgIGNvbnN0IHNjcmlwdF8gPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIpO1xuICAgICAgICAgICAgc2NyaXB0Xy5zZXRBdHRyaWJ1dGUoXCJkYXRhLWNmYXN5bmNcIiwgXCJmYWxzZVwiKTtcbiAgICAgICAgICAgIHNjcmlwdF8uaW5uZXJIVE1MID0gYGBcbiAgICAgICAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoc2NyaXB0Xyk7XG4gICAgICAgIH1cbiAgICB9LCBbcHJlbWl1bV0pO1xuXG4gICAgY29uc3QgaGFuZGxlQXZhdGFyQ2xpY2sgPSAoZXZlbnQpID0+IHtcbiAgICAgICAgc2V0QW5jaG9yRWwoZXZlbnQuY3VycmVudFRhcmdldCk7XG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZUNsb3NlID0gKCkgPT4ge1xuICAgICAgICBzZXRBbmNob3JFbChudWxsKTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIChcbiAgICAgICAgPE5vU3NyPlxuICAgICAgICAgICAgPE5vdGlmaWNhdGlvbnNQcm92aWRlcj5cbiAgICAgICAgICAgICAgICA8R2xvYmFsQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17e3NldFBsYXlpbmdTb25nRGF0YSwgc2V0QXVkaW9Mb2FkaW5nLCB3ZWJkYXZDb25maWdzLCBwcmVtaXVtIH19PlxuICAgICAgICAgICAgICAgICAgICA8VGhlbWVQcm92aWRlciB0aGVtZT17dGhlbWV9IG5vU3NyPjxDc3NCYXNlbGluZSAvPlxuICAgICAgICAgICAgICAgICAgICA8U3R5bGVkQXBwQmFyIHBvc2l0aW9uPVwic3RhdGljXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VG9vbGJhcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBvbkNsaWNrPXsoKSA9PiBmZXRjaFJhbmRvbU11c2ljKCl9IGNvbXBvbmVudD1cImRpdlwiIHN4PXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsZXhHcm93OiAwLjEsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJIYXJtb255T1MgU2FucyxzeXN0ZW0tdWlcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogLTAuNVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxTXVzaWMuY2NcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IG14OiAyLCBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzhweCcsIGZsZXhHcm93OiAxIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU2VhcmNoU3VibWl0fSBzdHlsZT17eyBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZmxleEdyb3c6IDEgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyF0b2tlbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFZhbHVlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVmFsdWUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnRBZG9ybm1lbnQ6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dEFkb3JubWVudCBwb3NpdGlvbj1cInN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaEljb24gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5wdXRBZG9ybm1lbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUeXBlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hUeXBlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWVudUl0ZW0gdmFsdWU9XCJzb25nc1wiPlNvbmdzPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSB2YWx1ZT1cImFydGlzdHNcIj5BcnRpc3RzPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvbiBjb2xvcj1cImluaGVyaXRcIiBvbkNsaWNrPXtoYW5kbGVBdmF0YXJDbGlja30+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXIgey4uLnN0cmluZ0F2YXRhcihjb29raWVbJ3VzZXJuYW1lJ10gPyBjb29raWVbJ3VzZXJuYW1lJ10gOiBudWxsKX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnUgYW5jaG9yRWw9e2FuY2hvckVsfSBvcGVuPXtvcGVufSBvbkNsb3NlPXtoYW5kbGVDbG9zZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucmVwbGFjZShjb29raWVbJ3VzZXJuYW1lJ10gPyAnL3VzZXInIDogJy9sb2dpbicpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUljb24+PEFjY291bnRDaXJjbGVJY29uIC8+PC9MaXN0SXRlbUljb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29va2llWyd1c2VybmFtZSddID8gY29va2llWyd1c2VybmFtZSddIDogdChcImxvZ2luXCIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWVudUl0ZW0gb25DbGljaz17aGFuZGxlTGFuZ3VhZ2VDbGlja30+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPjxHVHJhbnNsYXRlSWNvbiAvPjwvTGlzdEl0ZW1JY29uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3QoXCJzZXRMYW5ndWFnZVwiKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnUgYW5jaG9yRWw9e2xhbmd1YWdlQW5jaG9yRWx9IG9wZW49e2xhbmd1YWdlT3Blbn0gb25DbG9zZT17KCkgPT4gc2V0TGFuZ3VhZ2VBbmNob3JFbChudWxsKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7T2JqZWN0LmVudHJpZXMobGFuZ3VhZ2VzKS5tYXAoKFtsYW5nQ29kZSwgbGFuZ05hbWVdKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIGtleT17bGFuZ0NvZGV9IG9uQ2xpY2s9eygpID0+IGhhbmRsZUxhbmd1YWdlQ2hhbmdlKGxhbmdDb2RlKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsYW5nTmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Nvb2tpZVsndXNlcm5hbWUnXSAmJiA8TWVudUl0ZW0gb25DbGljaz17KCkgPT4gcm91dGVyLnJlcGxhY2UoY29va2llWyd1c2VybmFtZSddID8gJy91c2VyJyA6ICcvbG9naW4nKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPjxNYW5hZ2VBY2NvdW50c0ljb24gLz48L0xpc3RJdGVtSWNvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0KFwibWFuYWdlVXNlclwiKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT59XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sYmFyPlxuICAgICAgICAgICAgICAgICAgICA8L1N0eWxlZEFwcEJhcj5cbiAgICAgICAgICAgICAgICAgICAgPE11c2ljQ2FyZENvbnRhaW5lciBzb25ncz17c29uZ3NEYXRhfSBsb2FkaW5nPXtsb2FkaW5nfT48L011c2ljQ2FyZENvbnRhaW5lcj5cbiAgICAgICAgICAgICAgICAgICAgPFR1cm5zdGlsZVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICBzaXRlS2V5PVwiMHg0QUFBQUFBQkJ4TlQyZnRFV3ZzUnJFXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJpbnZpc2libGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25TdWNjZXNzPXsodG9rZW4pID0+IHNldFRva2VuKHRva2VuKX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPEJveCBzeD17eyBtdDogMC41LCB0ZXh0QWxpZ246IFwiY2VudGVyXCIgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBjb2xvcj1cInRleHRTZWNvbmRhcnlcIiBzeD17eyBtYjogMSB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDb250YWN0IG1lOiB1d3p6cnJyc3htQG91dGxvb2suY29tXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBjb2xvcj1cInRleHRTZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmY29weTsgMjAyNSAxTXVzaWMuY2MgQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgbXQ6MSxoZWlnaHQ6IGNvdmVySGVpZ2h0IH19PjwvQm94PlxuICAgICAgICAgICAgICAgICAgICA8QXVkaW9QbGF5ZXIgY292ZXJIZWlnaHQ9e2NvdmVySGVpZ2h0fSBzb25nRGF0YT17cGxheWluZ1NvbmdEYXRhfT48L0F1ZGlvUGxheWVyPlxuICAgICAgICAgICAgICAgICAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICAgICAgICAgICAgICAgPC9HbG9iYWxDb250ZXh0LlByb3ZpZGVyPlxuICAgICAgICAgICAgPC9Ob3RpZmljYXRpb25zUHJvdmlkZXI+XG4gICAgICAgIDwvTm9Tc3I+XG4gICAgKTtcbn1cblxuXG5cblxuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VMYXlvdXRFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsIkJveCIsIkFwcEJhciIsIlRvb2xiYXIiLCJUeXBvZ3JhcGh5IiwiSWNvbkJ1dHRvbiIsIlRleHRGaWVsZCIsIklucHV0QWRvcm5tZW50IiwiQXZhdGFyIiwiU2VsZWN0IiwiTWVudUl0ZW0iLCJMaXN0SXRlbUljb24iLCJNZW51IiwiY3JlYXRlVGhlbWUiLCJDc3NCYXNlbGluZSIsInVzZU1lZGlhUXVlcnkiLCJTZWFyY2hJY29uIiwiQWNjb3VudENpcmNsZUljb24iLCJHVHJhbnNsYXRlSWNvbiIsIk1hbmFnZUFjY291bnRzSWNvbiIsInN0eWxlZCIsIlRoZW1lUHJvdmlkZXIiLCJNdXNpY0NhcmRDb250YWluZXIiLCJBdWRpb1BsYXllciIsImF4aW9zIiwiTm90aWZpY2F0aW9uc1Byb3ZpZGVyIiwiTm9Tc3IiLCJiYWNrZW5kVXJsIiwibGFuZ3VhZ2VzIiwidXNlQ29va2llcyIsInVzZVJvdXRlciIsInVzZUxvY2FsZSIsInVzZVRyYW5zbGF0aW9ucyIsIlR1cm5zdGlsZSIsIkdsb2JhbENvbnRleHQiLCJTdHlsZWRBcHBCYXIiLCJ0aGVtZSIsImJhY2tncm91bmRDb2xvciIsInBhbGV0dGUiLCJiYWNrZ3JvdW5kIiwicGFwZXIiLCJjb2xvciIsInRleHQiLCJwcmltYXJ5IiwiYm94U2hhZG93IiwiYm9yZGVyQm90dG9tIiwiZGl2aWRlciIsImNvbG9yU2NoZW1lcyIsImRhcmsiLCJzdHJpbmdUb0NvbG9yIiwic3RyaW5nIiwiaGFzaCIsImkiLCJsZW5ndGgiLCJjaGFyQ29kZUF0IiwidmFsdWUiLCJ0b1N0cmluZyIsInNsaWNlIiwic3RyaW5nQXZhdGFyIiwibmFtZSIsInN4IiwiYmdjb2xvciIsImNoaWxkcmVuIiwiSG9tZSIsImNvb2tpZSIsInNldENvb2tpZSIsIndlYmRhdkNvbmZpZ3MiLCJzZXRXZWJkYXZDb25maWdzIiwic2VhcmNoVmFsdWUiLCJzZXRTZWFyY2hWYWx1ZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic29uZ3NEYXRhIiwic2V0U29uZ0RhdGEiLCJpc0F1ZGlvTG9hZGluZyIsInNldEF1ZGlvTG9hZGluZyIsInBsYXlpbmdTb25nRGF0YSIsInNldFBsYXlpbmdTb25nRGF0YSIsInNlYXJjaFR5cGUiLCJzZXRTZWFyY2hUeXBlIiwiYW5jaG9yRWwiLCJzZXRBbmNob3JFbCIsInByZW1pdW0iLCJzZXRQcmVtaXVtIiwidG9rZW4iLCJzZXRUb2tlbiIsImxhbmd1YWdlQW5jaG9yRWwiLCJzZXRMYW5ndWFnZUFuY2hvckVsIiwibGFuZ3VhZ2VPcGVuIiwiQm9vbGVhbiIsInQiLCJyb3V0ZXIiLCJsb2NhbGUiLCJvcGVuIiwicmVmIiwiaXNTbWFsbFNjcmVlbiIsImJyZWFrcG9pbnRzIiwiZG93biIsImNvdmVySGVpZ2h0IiwiaGFuZGxlTGFuZ3VhZ2VDbGljayIsImV2ZW50IiwiY3VycmVudFRhcmdldCIsImhhbmRsZUxhbmd1YWdlQ2hhbmdlIiwicGF0aCIsInB1c2giLCJoYW5kbGVTZWFyY2hTdWJtaXQiLCJwcmV2ZW50RGVmYXVsdCIsInRyaW0iLCJyZXNwb25zZSIsImdldCIsIndpdGhDcmVkZW50aWFscyIsInBhcmFtcyIsImRhdGEiLCJlcnJvciIsImNvbnNvbGUiLCJjdXJyZW50IiwicmVzZXQiLCJmZXRjaFJhbmRvbU11c2ljIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiY3JlZGVudGlhbHMiLCJvayIsIkVycm9yIiwianNvbiIsIm11c2ljX2xpc3QiLCJ1c2VyTGFuZyIsIm5hdmlnYXRvciIsImxhbmd1YWdlIiwidXNlckxhbmd1YWdlIiwibGFuZ1ByZWZpeCIsInNwbGl0Iiwic3VwcG9ydGVkTG9jYWxlcyIsIk9iamVjdCIsImtleXMiLCJkZXRlY3RlZExvY2FsZSIsImluY2x1ZGVzIiwicmVwbGFjZSIsInRoZW4iLCJyZWRpcmVjdGVkIiwicG9pbnRzIiwidXNlcm5hbWUiLCJ3ZWJkYXZfY29uZmlncyIsImNhdGNoIiwiZXJyIiwibG9nIiwic2NyaXB0XyIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsInNldEF0dHJpYnV0ZSIsImlubmVySFRNTCIsImhlYWQiLCJhcHBlbmRDaGlsZCIsImhhbmRsZUF2YXRhckNsaWNrIiwiaGFuZGxlQ2xvc2UiLCJQcm92aWRlciIsIm5vU3NyIiwicG9zaXRpb24iLCJ2YXJpYW50Iiwib25DbGljayIsImNvbXBvbmVudCIsImZsZXhHcm93IiwiZm9udFdlaWdodCIsImZvbnRGYW1pbHkiLCJsZXR0ZXJTcGFjaW5nIiwibXgiLCJkaXNwbGF5IiwiZ2FwIiwiZm9ybSIsIm9uU3VibWl0Iiwic3R5bGUiLCJhbGlnbkl0ZW1zIiwiZnVsbFdpZHRoIiwiZGlzYWJsZWQiLCJwbGFjZWhvbGRlciIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIklucHV0UHJvcHMiLCJzdGFydEFkb3JubWVudCIsInNpemUiLCJvbkNsb3NlIiwiZW50cmllcyIsIm1hcCIsImxhbmdDb2RlIiwibGFuZ05hbWUiLCJzb25ncyIsInNpdGVLZXkiLCJvblN1Y2Nlc3MiLCJtdCIsInRleHRBbGlnbiIsIm1iIiwiaGVpZ2h0Iiwic29uZ0RhdGEiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/page.js\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/utils.js":
/*!***********************************!*\
  !*** ./src/app/[locale]/utils.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDownloadLink: () => (/* binding */ checkDownloadLink),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fetchDownloadStatus: () => (/* binding */ fetchDownloadStatus),\n/* harmony export */   getCookie: () => (/* binding */ getCookie)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(ssr)/./src/app/[locale]/config.js\");\n\n\nfunction getCookie(name, cookies) {\n    const cookie = (cookies ? cookies : document.cookie).split(';');\n    for(let i = 0; i < cookie.length; i++){\n        const cookie_ = cookie[i].trim();\n        const cookieParts = cookie_.split('=');\n        if (cookieParts[0] === name) {\n            return cookieParts[1];\n        }\n    }\n    return null;\n}\nconst fetchDownloadStatus = async (songData, path)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.backendUrl + `${path}/`, songData, {\n            withCredentials: true\n        });\n        return response.data; // 返回 { detail: '...', download_url: '...', status: 200 或 202 }\n    } catch (error) {\n        console.log(error);\n        if (error.status >= 300 && error.status < 400) {\n            window.location.href = error.headers.Location;\n        }\n        throw error.response ? error.response.data : new Error('无法连接到服务器');\n    }\n};\n// 检查下载链接是否可用\nconst checkDownloadLink = async (url)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].head(url);\n        return response.status === 200;\n    } catch  {\n        return false;\n    }\n};\n// 创建 axios 实例\nconst axiosInstance = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create();\n// 请求拦截器：可以用于配置请求（可选）\naxiosInstance.interceptors.request.use((config)=>{\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器：处理重定向\naxiosInstance.interceptors.response.use((response)=>{\n    // 检查是否是 3xx 状态码（例如 301、302 等）\n    if (response.status >= 300 && response.status < 400) {\n        const redirectUrl = response.headers.Location;\n        console.log('重定向地址:', redirectUrl);\n        // 修改页面的 href 属性以进行重定向\n        window.location.href = redirectUrl;\n        // 返回一个 rejected promise，防止其他逻辑继续执行\n        return Promise.reject('页面已重定向');\n    }\n    return response; // 如果没有重定向，直接返回响应\n}, (error)=>{\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (axiosInstance);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/utils.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/component/Seo.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/component/Seo.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SEO)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n\n\nfunction SEO() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('Home');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: t(\"title\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 8,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"manifest\",\n                href: \"/manifest.json\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 9,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: t(\"description\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 10,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"viewport\",\n                content: \"width=device-width, initial-scale=1.0, user-scalable=no\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\component\\\\Seo.js\",\n        lineNumber: 7,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2NvbXBvbmVudC9TZW8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEM7QUFFM0IsU0FBU0M7SUFDcEIsTUFBTUMsSUFBSUYscURBQWVBLENBQUM7SUFFMUIscUJBQ0ksOERBQUNHOzswQkFDRyw4REFBQ0M7MEJBQU9GLEVBQUU7Ozs7OzswQkFDViw4REFBQ0c7Z0JBQUtDLEtBQUk7Z0JBQVdDLE1BQUs7Ozs7OzswQkFDMUIsOERBQUNDO2dCQUFLQyxNQUFLO2dCQUFjQyxTQUFTUixFQUFFOzs7Ozs7MEJBQ3BDLDhEQUFDTTtnQkFBS0MsTUFBSztnQkFBV0MsU0FBUTs7Ozs7Ozs7Ozs7O0FBRzFDIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcc3JjXFxhcHBcXFtsb2NhbGVdXFxjb21wb25lbnRcXFNlby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3VzZVRyYW5zbGF0aW9uc30gZnJvbSBcIm5leHQtaW50bFwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU0VPKCkge1xyXG4gICAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnSG9tZScpO1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGhlYWQ+XHJcbiAgICAgICAgICAgIDx0aXRsZT57dChcInRpdGxlXCIpfTwvdGl0bGU+XHJcbiAgICAgICAgICAgIDxsaW5rIHJlbD0nbWFuaWZlc3QnIGhyZWY9Jy9tYW5pZmVzdC5qc29uJz48L2xpbms+XHJcbiAgICAgICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e3QoXCJkZXNjcmlwdGlvblwiKX0vPlxyXG4gICAgICAgICAgICA8bWV0YSBuYW1lPSd2aWV3cG9ydCcgY29udGVudD0nd2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCwgdXNlci1zY2FsYWJsZT1ubycvPlxyXG4gICAgICAgIDwvaGVhZD5cclxuICAgICk7XHJcbn0iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb25zIiwiU0VPIiwidCIsImhlYWQiLCJ0aXRsZSIsImxpbmsiLCJyZWwiLCJocmVmIiwibWV0YSIsIm5hbWUiLCJjb250ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/component/Seo.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   webdavServiceUrl: () => (/* binding */ webdavServiceUrl)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst webdavServiceUrl = 'http://localhost:5001/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/config.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.js":
/*!************************************!*\
  !*** ./src/app/[locale]/layout.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material-nextjs/v15-appRouter */ \"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/i18n/routing */ \"(rsc)/./src/i18n/routing.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var _src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/component/Seo */ \"(rsc)/./src/app/[locale]/component/Seo.js\");\n\n\n\n\n\n\n\nasync function RootLayout({ children, params }) {\n    const { locale } = await params;\n    if (!_src_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            messages: messages,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_app_locale_component_Seo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_nextjs_v15_appRouter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n            lineNumber: 16,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\layout.js\",\n        lineNumber: 15,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQTBFO0FBQ2pDO0FBQ0k7QUFDRjtBQUNNO0FBQ0U7QUFFcEMsZUFBZU0sV0FBVyxFQUFDQyxRQUFRLEVBQUVDLE1BQU0sRUFBQztJQUN2RCxNQUFNLEVBQUNDLE1BQU0sRUFBQyxHQUFHLE1BQU1EO0lBQ3ZCLElBQUksQ0FBQ0wsc0RBQU9BLENBQUNPLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixTQUFTO1FBQ25DUix5REFBUUE7SUFDWjtJQUNBLE1BQU1XLFdBQVcsTUFBTVYsNERBQVdBO0lBQ2xDLHFCQUNJLDhEQUFDVztRQUFLQyxNQUFNTDtrQkFDWiw0RUFBQ0wsaURBQXNCQTtZQUFDUSxVQUFVQTs7OEJBQ2xDLDhEQUFDUCxxRUFBR0E7Ozs7OzhCQUNKLDhEQUFDVTs4QkFDRyw0RUFBQ2YsMEVBQXNCQTtrQ0FDbEJPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWpCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcd2ViXFwxbXVzaWNcXDFtdXNpY19mcm9udGVuZFxcc3JjXFxhcHBcXFtsb2NhbGVdXFxsYXlvdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtBcHBSb3V0ZXJDYWNoZVByb3ZpZGVyfSBmcm9tIFwiQG11aS9tYXRlcmlhbC1uZXh0anMvdjE1LWFwcFJvdXRlclwiO1xuaW1wb3J0IHtub3RGb3VuZH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHtnZXRNZXNzYWdlc30gZnJvbSBcIm5leHQtaW50bC9zZXJ2ZXJcIjtcbmltcG9ydCB7cm91dGluZ30gZnJvbSBcIkAvc3JjL2kxOG4vcm91dGluZ1wiO1xuaW1wb3J0IHtOZXh0SW50bENsaWVudFByb3ZpZGVyfSBmcm9tIFwibmV4dC1pbnRsXCI7XG5pbXBvcnQgU0VPIGZyb20gXCJAL3NyYy9hcHAvW2xvY2FsZV0vY29tcG9uZW50L1Nlb1wiO1xuXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBSb290TGF5b3V0KHtjaGlsZHJlbiwgcGFyYW1zfSkge1xuICAgIGNvbnN0IHtsb2NhbGV9ID0gYXdhaXQgcGFyYW1zO1xuICAgIGlmICghcm91dGluZy5sb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSkpIHtcbiAgICAgICAgbm90Rm91bmQoKTtcbiAgICB9XG4gICAgY29uc3QgbWVzc2FnZXMgPSBhd2FpdCBnZXRNZXNzYWdlcygpO1xuICAgIHJldHVybiAoXG4gICAgICAgIDxodG1sIGxhbmc9e2xvY2FsZX0+XG4gICAgICAgIDxOZXh0SW50bENsaWVudFByb3ZpZGVyIG1lc3NhZ2VzPXttZXNzYWdlc30+XG4gICAgICAgIDxTRU8gLz5cbiAgICAgICAgPGJvZHk+XG4gICAgICAgICAgICA8QXBwUm91dGVyQ2FjaGVQcm92aWRlcj5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICA8L0FwcFJvdXRlckNhY2hlUHJvdmlkZXI+XG4gICAgICAgIDwvYm9keT5cbiAgICAgICAgPC9OZXh0SW50bENsaWVudFByb3ZpZGVyPlxuICAgICAgICA8L2h0bWw+XG4gICAgKTtcbn1cbiJdLCJuYW1lcyI6WyJBcHBSb3V0ZXJDYWNoZVByb3ZpZGVyIiwibm90Rm91bmQiLCJnZXRNZXNzYWdlcyIsInJvdXRpbmciLCJOZXh0SW50bENsaWVudFByb3ZpZGVyIiwiU0VPIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwicGFyYW1zIiwibG9jYWxlIiwibG9jYWxlcyIsImluY2x1ZGVzIiwibWVzc2FnZXMiLCJodG1sIiwibGFuZyIsImJvZHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/page.js":
/*!**********************************!*\
  !*** ./src/app/[locale]/page.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GlobalContext: () => (/* binding */ GlobalContext),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   stringAvatar: () => (/* binding */ stringAvatar)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const GlobalContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call GlobalContext() from the server but GlobalContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\web\\1music\\1music_frontend\\src\\app\\[locale]\\page.js",
"GlobalContext",
);const stringAvatar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call stringAvatar() from the server but stringAvatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\web\\1music\\1music_frontend\\src\\app\\[locale]\\page.js",
"stringAvatar",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\web\\1music\\1music_frontend\\src\\app\\[locale]\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./src/i18n/request.js":
/*!*****************************!*\
  !*** ./src/i18n/request.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        locale = _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNoQjtBQUVsQyxpRUFBZUEsNERBQWdCQSxDQUFDLE9BQU8sRUFBQ0UsYUFBYSxFQUFDO0lBQ2xELHVEQUF1RDtJQUN2RCxJQUFJQyxTQUFTLE1BQU1EO0lBRW5CLHFDQUFxQztJQUNyQyxJQUFJLENBQUNDLFVBQVUsQ0FBQ0YsNkNBQU9BLENBQUNHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixTQUFTO1FBQzlDQSxTQUFTRiw2Q0FBT0EsQ0FBQ0ssYUFBYTtJQUNsQztJQUVBLE9BQU87UUFDSEg7UUFDQUksVUFBVSxDQUFDLE1BQU0seUVBQU8sR0FBZ0IsRUFBRUosT0FBTyxNQUFNLEdBQUdLLE9BQU87SUFDckU7QUFDSixFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJlcXVlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtnZXRSZXF1ZXN0Q29uZmlnfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcclxuaW1wb3J0IHtyb3V0aW5nfSBmcm9tICcuL3JvdXRpbmcnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoe3JlcXVlc3RMb2NhbGV9KSA9PiB7XHJcbiAgICAvLyBUaGlzIHR5cGljYWxseSBjb3JyZXNwb25kcyB0byB0aGUgYFtsb2NhbGVdYCBzZWdtZW50XHJcbiAgICBsZXQgbG9jYWxlID0gYXdhaXQgcmVxdWVzdExvY2FsZTtcclxuXHJcbiAgICAvLyBFbnN1cmUgdGhhdCBhIHZhbGlkIGxvY2FsZSBpcyB1c2VkXHJcbiAgICBpZiAoIWxvY2FsZSB8fCAhcm91dGluZy5sb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSkpIHtcclxuICAgICAgICBsb2NhbGUgPSByb3V0aW5nLmRlZmF1bHRMb2NhbGU7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICBsb2NhbGUsXHJcbiAgICAgICAgbWVzc2FnZXM6IChhd2FpdCBpbXBvcnQoYC4uLy4uL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHRcclxuICAgIH07XHJcbn0pOyJdLCJuYW1lcyI6WyJnZXRSZXF1ZXN0Q29uZmlnIiwicm91dGluZyIsInJlcXVlc3RMb2NhbGUiLCJsb2NhbGUiLCJsb2NhbGVzIiwiaW5jbHVkZXMiLCJkZWZhdWx0TG9jYWxlIiwibWVzc2FnZXMiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.js\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.js":
/*!*****************************!*\
  !*** ./src/i18n/routing.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n/* harmony import */ var _src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/app/[locale]/config */ \"(rsc)/./src/app/[locale]/config.js\");\n\n\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_1__.defineRouting)({\n    locales: Object.keys(_src_app_locale_config__WEBPACK_IMPORTED_MODULE_0__.languages),\n    defaultLocale: 'en'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi9yb3V0aW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNNO0FBQ0Y7QUFFN0MsTUFBTUcsVUFBVUgsZ0VBQWFBLENBQUM7SUFDakNJLFNBQVNDLE9BQU9DLElBQUksQ0FBQ0osNkRBQVNBO0lBQzlCSyxlQUFlO0FBQ25CLEdBQUciLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGkxOG5cXHJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZWZpbmVSb3V0aW5nfSBmcm9tICduZXh0LWludGwvcm91dGluZyc7XHJcbmltcG9ydCB7Y3JlYXRlTmF2aWdhdGlvbn0gZnJvbSAnbmV4dC1pbnRsL25hdmlnYXRpb24nO1xyXG5pbXBvcnQge2xhbmd1YWdlc30gZnJvbSBcIkAvc3JjL2FwcC9bbG9jYWxlXS9jb25maWdcIjtcclxuXHJcbmV4cG9ydCBjb25zdCByb3V0aW5nID0gZGVmaW5lUm91dGluZyh7XHJcbiAgICBsb2NhbGVzOiBPYmplY3Qua2V5cyhsYW5ndWFnZXMpLFxyXG4gICAgZGVmYXVsdExvY2FsZTogJ2VuJ1xyXG59KTsiXSwibmFtZXMiOlsiZGVmaW5lUm91dGluZyIsImNyZWF0ZU5hdmlnYXRpb24iLCJsYW5ndWFnZXMiLCJyb3V0aW5nIiwibG9jYWxlcyIsIk9iamVjdCIsImtleXMiLCJkZWZhdWx0TG9jYWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@formatjs","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@emotion","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/prop-types","vendor-chunks/next-intl","vendor-chunks/stylis","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/hoist-non-react-statics","vendor-chunks/asynckit","vendor-chunks/react-is","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/object-assign","vendor-chunks/@swc","vendor-chunks/@babel","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@toolpad","vendor-chunks/react-transition-group","vendor-chunks/universal-cookie","vendor-chunks/react-cookie","vendor-chunks/@marsidev"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.js&appDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5Cweb%5C1music%5C1music_frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();