"""
Django settings for onemusic_backend project.

Generated by 'django-admin startproject' using Django 5.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""
import datetime
from pathlib import Path
import os
from django.utils.translation import gettext_lazy as _

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
DEBUG=False


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-enh3gcir$=4qr$wt)lk4rnw57_y8%hk&1apc9#)m6=p+_p5j)s'
DOWNLOADER_SECRET='1p5kh&FPTA9W2VLF=n6~&J]u1cx'
# SECURITY WARNING: don't run with debug turned on in production!


ALLOWED_HOSTS = [
    '1music.cc',
    'dev.1music.cc',
    '*'
]

JWT_CONFIG = {
    'ALGORITHM': 'HS256',  # 使用 sha256
    'EXPIRATION_DELTA': datetime.timedelta(minutes=30),
}

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'backend.apps.BackendConfig',
]

MIDDLEWARE = [
    'backend.middleware.LocaleMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'onemusic_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'onemusic_backend.wsgi.application'

# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',  # 指定 MySQL
        'NAME': 'onemusic',         # 你的数据库名
        'USER': 'onemusic',            # 你的 MySQL 用户名
        'PASSWORD': '33tyWiJTYBZArFQF',    # 你的 MySQL 密码
        'HOST': '*************',                  # 服务器地址，远程数据库填 IP
        'PORT': '3306',                       # MySQL 端口（默认 3306）
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",  # 避免 SQL 兼容性问题
        }
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'zh-CN'

TIME_ZONE = 'UTC'

USE_I18N = True

LANGUAGES = [
    ('zh-CN', _('Simplified Chinese')),  # 默认语言
    ('ar', _('Arabic')),
    ('de', _('German')),
    ('en', _('English')),
    ('es', _('Spanish')),
    ('fr', _('French')),
    ('hi', _('Hindi')),
    ('it', _('Italian')),
    ('ja', _('Japanese')),
    ('ko', _('Korean')),
    ('nl', _('Dutch')),
    ('pt', _('Portuguese')),
    ('ru', _('Russian')),
    ('tr', _('Turkish')),
    ('zh-TW', _('Traditional Chinese')),
]

LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale'),  # 翻译文件存放路径
]

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'backend/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

TURNSTILE_SECRET_KEY = '0x4AAAAAAA8xnzkYcj4rZRSxxreIbE0b26s'

DOWNLOAD_SERVER_URL='https://oss.1music.cc'
SEARCH_SERVER_URL='https://api.1music.cc/search'
WEBDAV_SERVER_URL='http://localhost:5001'  # WebDAV服务地址

# WebDAV JWT配置
WEBDAV_JWT_SECRET = 'webdav_jwt_secret_2024_v1'
WEBDAV_JWT_EXPIRATION_HOURS = 24  # JWT过期时间（小时）

# 默认下载分区配置
DEFAULT_DOWNLOAD_PARTITION = 0

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.email.ap-mumbai-1.oci.oraclecloud.com'                        # QQ SMTP 服务器地址
EMAIL_PORT = 587                                  # 使用 SSL 的端口
EMAIL_USE_TLS = True                            # 不使用 TLS
EMAIL_USE_SSL = False                              # 启用 SSL 加密
EMAIL_HOST_USER = '<EMAIL>'             # 你的 QQ 邮箱地址
EMAIL_HOST_PASSWORD = 'O.4(OLukcw$U}P2tP}sL'       # QQ 邮箱的 SMTP 授权码
DEFAULT_FROM_EMAIL = '<EMAIL>'              # 默认发送人
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles") 

CSRF_TRUSTED_ORIGINS = [
    'https://1music.cc',
    'https://dev.1music.cc',
    "http://*",
    "https://*",
]

# 配置支付密钥
PAYMENT_KEY = "4p6DjSBWnBMaj6RjsN6Zu6ejMSJa16uz"
PAYMENT_URL = "https://www.sailapay.com/mapi.php"
MERCHANT_ID = 1246  # 商户ID

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.db.DatabaseCache",
        "LOCATION": "my_cache_table",
    }
}

