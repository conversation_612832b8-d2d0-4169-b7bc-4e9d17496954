from django.contrib import admin
from django.urls import path

from backend.pay import list_products, create_order, payment_notify
from backend.redeem import manage_redeem_codes, delete_code
from backend.user import user_downloaded_music, delete_downloaded_music, user_upload_tasks, delete_upload_task
from backend.views import login_view, logout_view, send_verification_email, verify_email, get_user_profile_view, \
    cached_random_music
from backend.downloader import download_music_view, preview_music_view, update_download_status
from backend.webdav import remove_webdav_config_view, update_upload_task_status_view, webdav_add_config_callback_view

urlpatterns = [
    path("backend/admin/redeem-codes/", manage_redeem_codes, name="manage_redeem_codes"),
    path("backend/admin/delete-code/<int:code_id>/", delete_code, name="delete_code"),
    path('backend/admin',admin.site.urls),
    path('backend/send_verification_email', send_verification_email, name='register'),
    path('backend/verify_email',verify_email, name='verify_email'),
    path('backend/login/', login_view, name='login'),
    path('backend/logout/', logout_view, name='logout'),
    path('backend/get_user_profile/', get_user_profile_view, name='preview'),
    path('backend/remove_webdav_config/',remove_webdav_config_view,name='remove_webdav_config'),
    path('backend/download/', download_music_view, name='download_music'),
    path('backend/preview/', preview_music_view, name='preview_music_view'),
    path('backend/update_download_status/',update_download_status, name='update_download_status'),

    path('backend/update_upload_task_status/',update_upload_task_status_view,name='update_upload_task_status'),
    path('backend/webdav_add_config_callback/',webdav_add_config_callback_view,name='webdav_add_config_callback'),
    path('backend/user_downloaded_music/',user_downloaded_music,name='user_downloaded_music'),
    path('backend/downloaded-music/<str:title>/<str:album>/<str:artist>/delete/', delete_downloaded_music, name='delete_downloaded_music'),
    path('backend/user_upload_tasks/',user_upload_tasks,name='user_upload_tasks'),
    path('backend/delete_task/<str:task_id>/',delete_upload_task,name='delete_upload_task'),
    path('backend/',cached_random_music,name='get_random_music'),

    path('backend/products/',list_products,name='list_products'),
    path('backend/create_order/',create_order,name='create_order'),
    path('backend/pay_notify/',payment_notify,name='payment_notify'),
]
