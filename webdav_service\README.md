# WebDAV Service

独立的WebDAV验证和上传服务，负责处理音频文件的转码和WebDAV上传任务。

## 功能特性

- WebDAV连接验证
- 音频文件转码（支持多种格式：mp3, flac, wav, aac, m4a, ogg）
- 基于Celery的异步上传任务
- 签名验证机制
- 任务状态回调

## 项目结构

```
webdav_service/
├── app.py              # Flask主应用
├── config.py           # 配置文件
├── utils.py            # 工具函数
├── requirements.txt    # Python依赖
├── Dockerfile         # Docker构建文件
├── docker-compose.yml # Docker编排文件
├── start_celery.py    # Celery启动脚本
└── README.md          # 说明文档
```

## 安装和运行

### 使用Docker Compose（推荐）

1. 构建和启动服务：
```bash
docker-compose up -d
```

2. 查看日志：
```bash
docker-compose logs -f
```

### 手动安装

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 安装FFmpeg：
```bash
# Ubuntu/Debian
sudo apt-get install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg

# macOS
brew install ffmpeg
```

3. 启动Flask应用：
```bash
python app.py
```

4. 启动Celery Worker：
```bash
celery -A app.celery worker --loglevel=info --queues=webdav_upload
```

## API接口

### 1. 添加WebDAV配置

**POST** `/add_config`

请求体：
```json
{
    "jwt_token": "用户JWT令牌",
    "url": "https://webdav.example.com",
    "username": "user",
    "password": "pass"
}
```

响应：
```json
{
    "success": true,
    "message": "WebDAV configuration added successfully",
    "config_id": 1,
    "signature": "生成的签名"
}
```

### 2. 上传音频文件

**POST** `/upload`

请求体：
```json
{
    "song_hash": "SHA256验证哈希",
    "song_title": "歌曲名",
    "song_artist": "艺术家",
    "album": "专辑名",
    "video_id": "视频ID",
    "format": "mp3",
    "cover_url": "封面图片的完整URL",
    "webdav_config": {
        "id": 1,
        "url": "https://webdav.example.com",
        "username": "user",
        "password": "pass",
        "signature": "预生成的签名"
    }
}
```

响应：
```json
{
    "success": true,
    "message": "Upload task started",
    "task_id": "生成的任务ID"
}
```

## 配置说明

### 环境变量

- `BACKEND_URL`: 后端服务地址
- `DOWNLOAD_SERVER_URL`: 下载服务器地址
- `REDIS_HOST`: Redis主机地址
- `REDIS_PORT`: Redis端口
- `REDIS_PASSWORD`: Redis密码
- `REDIS_DB_CELERY`: Celery使用的Redis数据库
- `LOG_LEVEL`: 日志级别

### 支持的音频格式

- MP3 (libmp3lame)
- FLAC
- WAV (PCM)
- AAC
- M4A (AAC)
- OGG (Vorbis)

## 工作流程

### 添加WebDAV配置流程

1. 用户从后端获取WebDAV JWT令牌
2. 用户携带JWT向WebDAV服务发送添加配置请求
3. WebDAV服务验证JWT和WebDAV连接
4. WebDAV服务向后端发送添加配置回调
5. 后端创建配置并生成签名
6. 返回配置ID和签名给WebDAV服务
7. WebDAV服务返回成功响应给用户

### 音频上传流程

1. 用户从后端profile接口获取带签名的WebDAV配置
2. 用户直接向WebDAV服务发送上传请求（包含歌曲信息和WebDAV配置）
3. WebDAV服务验证配置签名
4. 启动Celery任务：
   - **轮询后端获取下载链接**（最多20次，间隔2秒，参考前端逻辑）
   - **下载原始音频文件**（webm格式）
   - **下载封面图片**（webp格式，自动裁剪为正方形并转为jpg）
   - **转码为指定格式**（使用FFmpeg，参考前端转码器逻辑）
   - **上传到WebDAV服务器**
5. 清理临时文件

## 下载和转码逻辑

### 下载逻辑（参考前端downloadManager）

1. **轮询下载状态**：
   - 使用用户传入的song_hash（SHA256）和video_id向后端发送下载请求
   - 最多重试20次，每次重试间隔2秒
   - 检查返回的download_url是否可用

2. **下载原始文件**：
   - 下载webm格式的原始音频文件
   - 使用流式下载，支持大文件

3. **下载封面图片**：
   - 使用用户传入的cover_url直接下载封面图片
   - 支持多种格式（webp, jpg, png等）
   - 如果下载失败，继续处理音频（封面可选）

### 图片处理逻辑（参考前端imageProcessor）

1. **格式转换**：
   - 将webp格式转换为jpg格式
   - 处理透明度，使用白色背景

2. **裁剪处理**：
   - 裁剪为正方形（取最小边长）
   - 调整大小为500x500像素
   - 使用高质量重采样算法

### 转码逻辑（参考前端audioTranscoder）

1. **FFmpeg命令构建**：
   - 根据目标格式设置不同的编码参数
   - MP3：libmp3lame编码器，320k比特率
   - FLAC：无损压缩
   - 其他格式：对应的最佳编码器

2. **元数据处理**：
   - 添加歌曲标题、艺术家、专辑信息
   - 嵌入封面图片（如果可用）
   - 添加自定义标识（1music.cc）

3. **封面嵌入**：
   - MP3：使用mjpeg编码，ID3v2标签
   - FLAC：使用attached_pic disposition

## 安全机制

- 使用HMAC-SHA256签名验证WebDAV配置
- 密钥与后端服务保持一致
- 任务状态回调使用相同密钥验证

## 监控和日志

- 所有操作都有详细的日志记录
- 支持配置日志级别
- Celery任务状态可通过Redis监控

## 故障排除

1. **连接WebDAV失败**
   - 检查URL格式是否正确
   - 验证用户名和密码
   - 确认网络连接

2. **转码失败**
   - 检查FFmpeg是否正确安装
   - 确认原始文件格式支持
   - 查看详细错误日志

3. **上传失败**
   - 检查WebDAV服务器空间
   - 验证文件权限
   - 确认网络稳定性
