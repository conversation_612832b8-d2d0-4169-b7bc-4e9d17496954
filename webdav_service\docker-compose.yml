version: '3.8'

services:
  webdav-api:
    build: .
    ports:
      - "5001:5001"
    environment:
      - BACKEND_URL=https://1music.cc/backend/
      - DOWNLOAD_SERVER_URL=https://oss.1music.cc
      - REDIS_HOST=1Panel-redis-6Fqj
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_b5iJDa
      - REDIS_DB_CELERY=1
      - LOG_LEVEL=INFO
    volumes:
      - ./temp:/app/temp
    restart: unless-stopped
    networks:
      - webdav-network

  webdav-worker:
    build: .
    command: celery -A app.celery worker --loglevel=info --queues=webdav_upload --concurrency=2
    environment:
      - BACKEND_URL=https://1music.cc/backend/
      - DOWNLOAD_SERVER_URL=https://oss.1music.cc
      - REDIS_HOST=1Panel-redis-6Fqj
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_b5iJDa
      - REDIS_DB_CELERY=1
      - LOG_LEVEL=INFO
    volumes:
      - ./temp:/app/temp
    restart: unless-stopped
    networks:
      - webdav-network
    depends_on:
      - webdav-api

networks:
  webdav-network:
    driver: bridge
