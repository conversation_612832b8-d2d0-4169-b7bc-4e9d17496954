"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/download/page.js":
/*!*******************************************!*\
  !*** ./src/app/[locale]/download/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CircularProgress,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/NoSsr */ \"(app-pages-browser)/./node_modules/@mui/material/NoSsr/NoSsr.js\");\n/* harmony import */ var _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/app/[locale]/utils/downloadManager */ \"(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DownloadPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"Download\");\n    const title = searchParams.get('title');\n    const album = searchParams.get('album');\n    const artist = searchParams.get('artist');\n    const videoId = searchParams.get('videoId');\n    const request_format = searchParams.get('request_format');\n    const song_hash = searchParams.get('song_hash');\n    const thumbnail = searchParams.get('thumbnail');\n    // Simple state management for parallel progress\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Progress state for parallel operations\n    const progressState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        urlFetch: 0,\n        ffmpegLoad: 0,\n        audioDownload: 0,\n        transcoding: 0 // 转码进度 (0-10%)\n    });\n    const downloadManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Calculate total progress from all components\n    const calculateTotalProgress = ()=>{\n        const { urlFetch, ffmpegLoad, audioDownload, transcoding } = progressState.current;\n        return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);\n    };\n    // Update specific progress component\n    const updateProgressComponent = (component, componentProgress)=>{\n        progressState.current[component] = componentProgress;\n        const totalProgress = calculateTotalProgress();\n        setProgress(totalProgress);\n        console.log(\"Progress Update - \".concat(component, \": \").concat(componentProgress, \"%, Total: \").concat(totalProgress, \"%\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DownloadPage.useEffect\": ()=>{\n            const script = document.createElement(\"script\");\n            script.type = \"text/javascript\";\n            script.src = \"//plantationexhaust.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js\";\n            document.head.appendChild(script);\n            const script_1 = document.createElement(\"script\");\n            script_1.type = \"text/javascript\";\n            script_1.src = \"//pl26003516.effectiveratecpm.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js\";\n            document.body.appendChild(script_1);\n            if (!title || !videoId || !request_format || !song_hash) {\n                setError(t(\"incomplete_song_info\"));\n                setIsLoading(false);\n                return;\n            }\n            const processDownload = {\n                \"DownloadPage.useEffect.processDownload\": async ()=>{\n                    try {\n                        // Reset progress state\n                        progressState.current = {\n                            urlFetch: 0,\n                            ffmpegLoad: 0,\n                            audioDownload: 0,\n                            transcoding: 0\n                        };\n                        setProgress(0);\n                        setError(null);\n                        // Initialize download manager\n                        downloadManagerRef.current = new _src_app_locale_utils_downloadManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n                        const songData = {\n                            title,\n                            album,\n                            artist,\n                            videoId,\n                            request_format: 'webm',\n                            song_hash,\n                            thumbnail\n                        };\n                        // Process download with component-based progress\n                        await downloadManagerRef.current.processDownloadWithComponents(songData, request_format, updateProgressComponent, t);\n                        setIsLoading(false);\n                    } catch (err) {\n                        console.error('Download error:', err);\n                        setError(err.message || t(\"download_failed\"));\n                        setIsLoading(false);\n                    } finally{\n                        // Clean up download manager\n                        if (downloadManagerRef.current) {\n                            downloadManagerRef.current.cleanup();\n                            downloadManagerRef.current = null;\n                        }\n                    }\n                }\n            }[\"DownloadPage.useEffect.processDownload\"];\n            processDownload();\n            // Cleanup function\n            return ({\n                \"DownloadPage.useEffect\": ()=>{\n                    if (downloadManagerRef.current) {\n                        downloadManagerRef.current.cleanup();\n                        downloadManagerRef.current = null;\n                    }\n                }\n            })[\"DownloadPage.useEffect\"];\n        }\n    }[\"DownloadPage.useEffect\"], [\n        title,\n        album,\n        artist,\n        videoId,\n        request_format,\n        song_hash,\n        thumbnail,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_NoSsr__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            flexDirection: \"column\",\n            height: \"100vh\",\n            sx: {\n                p: 3\n            },\n            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                sx: {\n                    minWidth: 300\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 60\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 131,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        sx: {\n                            mt: 2,\n                            mb: 3,\n                            textAlign: 'center'\n                        },\n                        children: title ? t(\"downloading\", {\n                            title: title\n                        }) : t(\"processing\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 132,\n                        columnNumber: 25\n                    }, undefined),\n                    progress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            width: '100%',\n                            mt: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            variant: \"determinate\",\n                            value: progress\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                            lineNumber: 137,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 136,\n                        columnNumber: 29\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 130,\n                columnNumber: 21\n            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        color: \"error\",\n                        sx: {\n                            mb: 2,\n                            textAlign: 'center'\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 143,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        sx: {\n                            textAlign: 'center'\n                        },\n                        children: t(\"try_again_later\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                        lineNumber: 146,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 142,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CircularProgress_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h6\",\n                    color: \"success.main\",\n                    sx: {\n                        textAlign: 'center'\n                    },\n                    children: t(\"download_complete\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                    lineNumber: 152,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n                lineNumber: 151,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n            lineNumber: 128,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\web\\\\1music\\\\1music_frontend\\\\src\\\\app\\\\[locale]\\\\download\\\\page.js\",\n        lineNumber: 127,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DownloadPage, \"ttHDDHzDTi3+1oWVB+Yd9IBeV24=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c = DownloadPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/download/page.js\n"));

/***/ })

});