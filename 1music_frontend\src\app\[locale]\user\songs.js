import React, {useState, useEffect, useContext} from "react";
import {
    Box,
    IconButton,
    Pagination,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
    Stack,
    Dialog,
    DialogTitle,
    CircularProgress,
    DialogContent,
    RadioGroup,
    FormControlLabel,
    Radio,
    DialogActions,
    Button
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import DeleteIcon from "@mui/icons-material/Delete";
import {backendUrl, webdavServiceUrl} from "@/src/app/[locale]/config";
import {useNotifications} from "@toolpad/core";
import {useTranslations} from "next-intl";
import {checkDownloadLink, fetchDownloadStatus} from "@/src/app/[locale]/utils";
import {GlobalContext} from "@/src/app/[locale]/user/page";

const DownloadDialog = ({ open, onClose, song }) => {
    const [format, setFormat] = useState('mp3');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedConfig, setSelectedConfig] = useState(null);
    const [selectedConfigId, setSelectedConfigId] = useState(null)
    const notifications = useNotifications();
    const { webdavConfigs, webdavJwt } = useContext(GlobalContext)
    const t = useTranslations("Download")

    const handleDownload = async () => {
        setIsLoading(true);
        setError(null);
        let retries = 0;
        const maxRetries = 20;

        const songData = {
            title: song.title,
            album: song.album,
            artist: song.artist,
            videoId: song.videoId,
            request_format: format,
            song_hash: song.song_hash,
            thumbnail: song.thumbnail
        };
        const key = notifications.show(t("downloading",{title: song.title}),{
            autoHideDuration: 40000
        })
        try {
            while (retries < maxRetries) {
                const status = await fetchDownloadStatus(songData,'download');

                if (status.download_url) {
                    let linkReadyRetries = 0;

                    // 轮询检查下载链接是否可用
                    while (linkReadyRetries < maxRetries) {
                        const isReady = await checkDownloadLink(status.download_url);
                        if (isReady) {
                            notifications.close(key)
                            window.location.href = status.download_url;
                            setIsLoading(false);
                            onClose();
                            return;
                        }
                        await new Promise((resolve) => setTimeout(resolve, 2000));
                        linkReadyRetries++;
                    }
                    throw new Error(t("download_unavailable"));
                }

                await new Promise((resolve) => setTimeout(resolve, 2000));
                retries++;
            }

            throw new Error(t("download_timeout"));
        } catch (err) {
            notifications.close(key)
            setError(err.detail || err.message);
            setIsLoading(false);
        }
    };

    const handleWebDavUpload = async () => {
        if (!selectedConfigId) {
            alert(t("select_webdav_config"));
            return;
        }

        if (!song || !song.title || !song.album || !song.artist || !song.videoId) {
            alert(t("incomplete_song_info"));
            return;
        }

        setIsLoading(true);
        setError(null);

        // 找到选中的WebDAV配置
        const selectedWebdavConfig = webdavConfigs.find(config => config.id === selectedConfigId);
        if (!selectedWebdavConfig) {
            alert(t("webdav_config_not_found"));
            setIsLoading(false);
            return;
        }

        // 构造WebDAV服务需要的数据
        const uploadData = {
            song_hash: song.song_hash,
            song_title: song.title,
            song_artist: song.artist,
            album: song.album,
            video_id: song.videoId,
            format: format,
            cover_url: song.thumbnail, // 直接使用封面URL
            webdav_config: {
                id: selectedWebdavConfig.id,
                url: selectedWebdavConfig.url,
                username: selectedWebdavConfig.username,
                password: selectedWebdavConfig.password,
                signature: selectedWebdavConfig.signature
            }
        };

        try {
            // 直接调用WebDAV服务
            const response = await fetch(webdavServiceUrl + 'upload', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(uploadData)
            });

            const result = await response.json();
            if (!response.ok) {
                throw new Error(result.error || t("upload_error"));
            }

            notifications.show(t("upload_success", {title: song.title}), {
                autoHideDuration: 2000
            });
            setIsLoading(false);
            onClose();
        } catch (err) {
            setError(err.message);
            setIsLoading(false);
        }
    };

    const handleWebDavConfigChange = (e) => {
        const configId = e.target.value;
        const selected = webdavConfigs.find(config => config.id === configId);
        setSelectedConfig(selected)
        setSelectedConfigId(configId);
    };

    return (
        <Dialog onClick={event => event.stopPropagation()} open={open} onClose={onClose}>
            <DialogTitle>{t("download_song",{title: song.title})}</DialogTitle>
            {isLoading? <Box display="flex" justifyContent="center" alignItems="center" height='80px'>
                <CircularProgress />
            </Box> : <DialogContent>
                <RadioGroup value={format} onChange={(e) => setFormat(e.target.value)}>
                    <FormControlLabel value="mp3" control={<Radio />} label="MP3" />
                    <FormControlLabel value="flac" control={<Radio />} label="FLAC" />
                </RadioGroup>
                {webdavConfigs.length > 0 && (
                    <>
                        <Typography variant="subtitle1">{t("select_webdav_config")}</Typography>
                        <RadioGroup value={selectedConfigId || ''} onChange={handleWebDavConfigChange}>
                            {webdavConfigs.map((config, index) => (
                                <FormControlLabel key={index} value={config.id} control={<Radio />} label={config.url} />
                            ))}
                        </RadioGroup>
                    </>
                )}
                {error && <Typography color="error">{error}</Typography>}
            </DialogContent>}
            <DialogActions>
                <Button onClick={handleDownload} disabled={isLoading}>{t("download")}</Button>
                <Button onClick={handleWebDavUpload} disabled={isLoading || webdavConfigs.length < 1}>{t("upload_to_webdav")}</Button>
                <Button onClick={onClose} disabled={isLoading}>{t("cancel")}</Button>
            </DialogActions>
        </Dialog>
    );
};


const SongsPage = () => {
    const [downloadedSongs, setDownloadedSongs] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [dialogOpen, setDialogOpen] = useState([...Array(30)].map(() => false))

    const fetchSong = (page) => {
        fetch(`${backendUrl}user_downloaded_music/?page=${page}`,{
            credentials:'include'
        })
            .then(response => response.json())
            .then(data => {
                setDownloadedSongs(data.downloaded_music);
                setTotalPages(data.total_pages);
            });
    }

    useEffect(() => {
        fetchSong(currentPage)
    }, [currentPage]);

    const handlePageChange = (event, value) => {
        setCurrentPage(value);
    };

    const handleDownload = (index) => {
        setDialogOpen(prev => {
            const newState = [...prev];
            newState[index] = true;
            return newState;
        });
    };

    const handleDelete = (song) => {
        fetch(`${backendUrl}downloaded-music/${song.title}/${song.album}/${song.artist}/delete/`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
            credentials:'include'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error("Failed to delete download record");
                }
                return response.json();
            })
            .then(() => {
                fetchSong(currentPage); // Refresh the page data after deletion
            })
            .catch(error => {
                console.error("Error deleting music:", error);
            });
    };


    return (
        <Box p={0}>
            <TableContainer>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell sx={{ width: '33.3%', wordWrap: 'break-word' }}>Title</TableCell>
                            <TableCell sx={{ width: '33.3%', wordWrap: 'break-word' }}>Album</TableCell>
                            <TableCell sx={{ width: '33.3%', wordWrap: 'break-word' }}>Artist</TableCell>
                            <TableCell sx={{ width: '10%' }} align="center">Actions</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {downloadedSongs.map((song, index) => (
                            <TableRow key={index}>
                                <DownloadDialog open={dialogOpen[index]} onClose={() => setDialogOpen([...Array(30)].map(() => false))} song={song} />
                                <TableCell sx={{ wordWrap: 'break-word' }}>{song.title}</TableCell>
                                <TableCell sx={{ wordWrap: 'break-word' }}>{song.album}</TableCell>
                                <TableCell sx={{ wordWrap: 'break-word' }}>{song.artist}</TableCell>
                                <TableCell align="center">
                                    <Stack direction="row" spacing={1} justifyContent="center">
                                        <IconButton onClick={() => handleDownload(index)} color='inherit'>
                                            <DownloadIcon />
                                        </IconButton>
                                        <IconButton color="error" onClick={() => handleDelete(song)}>
                                            <DeleteIcon />
                                        </IconButton>
                                    </Stack>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            {downloadedSongs.length > 0 ? <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                sx={{ mt: 3 }}
            /> : <Typography variant="h6" color="textSecondary" sx={{ mt: 3, textAlign: 'center' }}>
                No downloaded records
            </Typography>}
        </Box>
    );
};

export default SongsPage;
