# WebDAV架构重构总结

## 概述

根据新需求，WebDAV逻辑已经从后端分离到独立的Flask项目中，实现了基于JWT的认证机制和签名存储优化。

## 架构变更

### 1. 后端变更 (onemusic_backend)

#### 数据库模型更新
- **WebdavConfig模型**：添加了`signature`字段用于存储预生成的签名
- **UploadTask模型**：添加了`webdav_config`、`format_type`、`created_at`、`updated_at`字段

#### 新增接口
- `GET /backend/profile/`：返回用户信息，包含带签名的WebDAV配置和JWT令牌
- `POST /backend/webdav_add_config_callback/`：WebDAV服务添加配置的回调接口
- `POST /backend/update_upload_task_status/`：更新上传任务状态接口

#### JWT机制
- 生成WebDAV JWT令牌，包含用户邮箱和过期时间
- JWT密钥：`webdav_jwt_secret_2024_v1`
- 默认过期时间：24小时

#### 签名机制
- WebDAV配置签名使用HMAC-SHA256
- 签名密钥：`webdav_1music_secret_2024_v1`
- 签名格式：`url|username|password|config_id`

### 2. WebDAV服务 (webdav_service)

#### 新的独立Flask项目
- 基于Flask + Celery的异步处理架构
- 支持多种音频格式转码：mp3, flac, wav, aac, m4a, ogg
- 使用FFmpeg进行音频转码
- 使用webdav3客户端进行WebDAV操作

#### 主要接口
1. **添加配置接口** `POST /add_config`
   - 验证用户JWT令牌
   - 测试WebDAV连接
   - 向后端发送添加配置回调

2. **上传音频接口** `POST /upload`
   - 验证WebDAV配置签名
   - 启动异步转码和上传任务

#### Celery任务
- **轮询下载状态**：使用用户传入的song_hash和video_id，参考downloader.py接口，最多20次重试，间隔2秒
- **下载原始音频文件**：从下载服务器获取webm格式文件
- **下载封面图片**：使用用户传入的cover_url直接下载封面图片，支持多种格式，裁剪为正方形并转为jpg
- **音频转码**：使用FFmpeg转码为目标格式，嵌入封面和元数据
- **WebDAV上传**：上传到用户指定的WebDAV服务器
- **清理临时文件**：自动清理所有临时文件

## 下载和转码逻辑

### 参考前端实现

WebDAV服务的下载和转码逻辑完全参考前端的实现，确保一致性：

#### 1. 下载逻辑（参考downloadManager.js）

```javascript
// 前端轮询逻辑
async _getDownloadUrlWithProgress(songData) {
    const maxRetries = 20;
    const retryDelay = 2000;

    for (let retries = 0; retries < maxRetries; retries++) {
        const status = await fetchDownloadStatus(songData, 'download');
        if (status.download_url) {
            const isReady = await checkDownloadLink(status.download_url);
            if (isReady) {
                return status.download_url;
            }
        }
        await this._delay(retryDelay);
    }
}
```

**后端实现**：
- 使用相同的重试次数（20次）和间隔（2秒）
- 调用后端的`/download/`接口获取下载状态
- 验证下载链接的可用性

#### 2. 图片处理逻辑（参考imageProcessor.js）

```javascript
// 前端图片处理
static async cropToSquareJPEG(imageFile, size = 500, quality = 0.9) {
    // 裁剪为正方形
    const cropSize = Math.min(img.width, img.height);
    const offsetX = (img.width - cropSize) / 2;
    const offsetY = (img.height - cropSize) / 2;

    // 转换为JPEG
    canvas.toBlob((blob) => resolve(blob), 'image/jpeg', quality);
}
```

**后端实现**：
- 使用PIL库实现相同的裁剪逻辑
- 处理webp到jpg的格式转换
- 保持相同的尺寸（500x500）和质量设置

#### 3. 转码逻辑（参考audioTranscoder.js）

```javascript
// 前端转码命令构建
if (format === 'mp3') {
    command.push(
        '-codec:a', 'libmp3lame',
        '-b:a', '320k'
    );
    if (coverImageFile) {
        command.push(
            '-c:v', 'mjpeg',
            '-id3v2_version', '3',
            '-metadata:s:v', 'title=Album cover'
        );
    }
}
```

**后端实现**：
- 使用相同的FFmpeg参数和编码器
- 保持相同的比特率设置（320k）
- 使用相同的元数据标签

## 工作流程

### 添加WebDAV配置流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Backend as 后端
    participant WebDAV as WebDAV服务
    
    User->>Backend: 获取profile信息
    Backend->>User: 返回WebDAV JWT令牌
    User->>WebDAV: 发送添加配置请求(携带JWT)
    WebDAV->>WebDAV: 验证JWT和测试连接
    WebDAV->>Backend: 发送添加配置回调
    Backend->>Backend: 创建配置并生成签名
    Backend->>WebDAV: 返回配置ID和签名
    WebDAV->>User: 返回成功响应
```

### 音频上传流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Backend as 后端
    participant WebDAV as WebDAV服务
    participant DownloadServer as 下载服务器
    participant UserWebDAV as 用户WebDAV服务器
    
    User->>Backend: 获取profile信息
    Backend->>User: 返回带签名的WebDAV配置
    User->>WebDAV: 发送上传请求(歌曲信息+WebDAV配置)
    WebDAV->>WebDAV: 验证配置签名
    WebDAV->>DownloadServer: 下载原始音频文件
    WebDAV->>WebDAV: 转码为目标格式
    WebDAV->>UserWebDAV: 上传到用户WebDAV服务器
    WebDAV->>User: 返回任务ID
```

## 部署说明

### 后端部署
1. 更新数据库模型（需要运行migration）
2. 确保新的配置项已添加到settings.py
3. 重启Django服务

### WebDAV服务部署
1. 使用Docker Compose部署：
   ```bash
   cd webdav_service
   docker-compose up -d
   ```

2. 或手动部署：
   ```bash
   pip install -r requirements.txt
   python app.py  # 启动Flask应用
   celery -A app.celery worker --loglevel=info --queues=webdav_upload  # 启动Worker
   ```

## 配置说明

### 环境变量
- `BACKEND_URL`: 后端服务地址
- `DOWNLOAD_SERVER_URL`: 下载服务器地址
- `REDIS_HOST`: Redis主机地址
- `REDIS_PASSWORD`: Redis密码
- `WEBDAV_SERVER_URL`: WebDAV服务地址（在后端settings中配置）

### 密钥配置
- `WEBDAV_SECRET_KEY`: WebDAV通信密钥
- `WEBDAV_JWT_SECRET`: WebDAV JWT密钥

## 安全机制

1. **JWT认证**：用户添加配置时需要有效的JWT令牌
2. **签名验证**：所有WebDAV配置都有预生成的签名，防止篡改
3. **密钥隔离**：WebDAV服务使用独立的通信密钥
4. **连接验证**：添加配置前会测试WebDAV连接的有效性

## 优势

1. **性能优化**：签名预生成，避免每次请求都计算签名
2. **架构解耦**：WebDAV逻辑独立部署，便于扩展和维护
3. **安全增强**：多层验证机制，确保操作安全性
4. **用户体验**：异步处理，不阻塞用户操作
5. **格式支持**：支持多种音频格式转码

## 注意事项

1. 确保Redis服务正常运行，Celery依赖Redis作为消息队列
2. 需要安装FFmpeg用于音频转码
3. WebDAV服务需要能够访问下载服务器和后端服务
4. 建议配置适当的日志级别用于监控和调试
5. 生产环境建议使用反向代理（如Nginx）
