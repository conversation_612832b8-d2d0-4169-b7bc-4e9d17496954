"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/app/[locale]/config.js":
/*!************************************!*\
  !*** ./src/app/[locale]/config.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendUrl: () => (/* binding */ backendUrl),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   webdavServiceUrl: () => (/* binding */ webdavServiceUrl)\n/* harmony export */ });\nconst backendUrl = 'http://*************:5000/backend/';\nconst webdavServiceUrl = 'http://localhost:5001/';\nconst languages = {\n    \"ar\": \"العربية\",\n    \"de\": \"Deutsch\",\n    \"en\": \"English\",\n    \"es\": \"Español\",\n    \"fr\": \"Français\",\n    \"hi\": \"हिन्दी\",\n    \"it\": \"Italiano\",\n    \"ja\": \"日本語\",\n    \"ko\": \"한국어\",\n    \"nl\": \"Nederlands\",\n    \"pt\": \"Português\",\n    \"ru\": \"Русский\",\n    \"tr\": \"Türkçe\",\n    \"zh-CN\": \"简体中文\",\n    \"zh-TW\": \"繁體中文\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL2FwcC9bbG9jYWxlXS9jb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sTUFBTUEsYUFBYSxxQ0FBb0M7QUFDdkQsTUFBTUMsbUJBQW1CLHlCQUF3QjtBQUVqRCxNQUFNQyxZQUFZO0lBQ3JCLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixTQUFTO0lBQ1QsU0FBUztBQUNiLEVBQUUiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFx3ZWJcXDFtdXNpY1xcMW11c2ljX2Zyb250ZW5kXFxzcmNcXGFwcFxcW2xvY2FsZV1cXGNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgYmFja2VuZFVybCA9ICdodHRwOi8vMTcyLjI1Ljc5LjEyMjo1MDAwL2JhY2tlbmQvJ1xuZXhwb3J0IGNvbnN0IHdlYmRhdlNlcnZpY2VVcmwgPSAnaHR0cDovL2xvY2FsaG9zdDo1MDAxLydcblxuZXhwb3J0IGNvbnN0IGxhbmd1YWdlcyA9IHtcbiAgICBcImFyXCI6IFwi2KfZhNi52LHYqNmK2KlcIixcbiAgICBcImRlXCI6IFwiRGV1dHNjaFwiLFxuICAgIFwiZW5cIjogXCJFbmdsaXNoXCIsXG4gICAgXCJlc1wiOiBcIkVzcGHDsW9sXCIsXG4gICAgXCJmclwiOiBcIkZyYW7Dp2Fpc1wiLFxuICAgIFwiaGlcIjogXCLgpLngpL/gpKjgpY3gpKbgpYBcIixcbiAgICBcIml0XCI6IFwiSXRhbGlhbm9cIixcbiAgICBcImphXCI6IFwi5pel5pys6KqeXCIsXG4gICAgXCJrb1wiOiBcIu2VnOq1reyWtFwiLFxuICAgIFwibmxcIjogXCJOZWRlcmxhbmRzXCIsXG4gICAgXCJwdFwiOiBcIlBvcnR1Z3XDqnNcIixcbiAgICBcInJ1XCI6IFwi0KDRg9GB0YHQutC40LlcIixcbiAgICBcInRyXCI6IFwiVMO8cmvDp2VcIixcbiAgICBcInpoLUNOXCI6IFwi566A5L2T5Lit5paHXCIsXG4gICAgXCJ6aC1UV1wiOiBcIue5gemrlOS4reaWh1wiXG59OyJdLCJuYW1lcyI6WyJiYWNrZW5kVXJsIiwid2ViZGF2U2VydmljZVVybCIsImxhbmd1YWdlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./src/app/[locale]/config.js\n");

/***/ })

});