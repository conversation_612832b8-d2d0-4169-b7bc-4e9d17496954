"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/download/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js":
/*!***************************************************!*\
  !*** ./src/app/[locale]/utils/downloadManager.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadManager: () => (/* binding */ DownloadManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./audioTranscoder */ \"(app-pages-browser)/./src/app/[locale]/utils/audioTranscoder.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/app/[locale]/utils.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Optimized Download Manager with parallel processing\n * Handles concurrent ffmpeg loading, download URL fetching, and file downloads\n */ \n\n\nclass DownloadManager {\n    /**\n     * Update specific progress component\n     * @param {string} component - Progress component name\n     * @param {number} progress - Progress value for this component\n     */ _updateProgressComponent(component, progress) {\n        if (this.progressComponentCallback) {\n            this.progressComponentCallback(component, progress);\n        }\n    }\n    /**\n     * Process download with component-based progress tracking\n     * @param {Object} songData - Song information\n     * @param {string} requestFormat - Requested audio format\n     * @param {Function} onProgressComponent - Component progress callback\n     * @param {Function} t - Translation function\n     */ async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t1) {\n        this.abortController = new AbortController();\n        this.progressComponentCallback = onProgressComponent;\n        try {\n            // Reset progress state\n            this.progressState = {\n                urlFetch: 0,\n                ffmpegLoad: 0,\n                audioDownload: 0,\n                transcoding: 0\n            };\n            this._updateProgressComponent('urlFetch', 0);\n            // Initialize transcoder\n            this.transcoder = new _audioTranscoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n            // Set up transcoding progress callback (maps to 0-10% of transcoding component)\n            this.transcoder.setProgressCallback((param)=>{\n                let { progress } = param;\n                const transcodingProgress = progress * 10; // 0-10%\n                this._updateProgressComponent('transcoding', transcodingProgress);\n            });\n            // Start parallel operations\n            const ffmpegLoadPromise = requestFormat !== 'webm' ? this._loadFFmpegWithProgress() : this._skipFFmpegLoad();\n            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData);\n            // Pre-fetch thumbnail (optional, don't block on failure)\n            const thumbnailPromise = songData.thumbnail ? this._fetchThumbnail(songData.thumbnail) : Promise.resolve(null);\n            // Get download URL first (don't wait for FFmpeg if not needed yet)\n            const originalAudioUrl = await downloadUrlPromise;\n            // Start audio download immediately after getting URL\n            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl);\n            // For webm format, we don't need FFmpeg, so download and return immediately\n            if (requestFormat === 'webm') {\n                const audioBlob = await audioDownloadPromise;\n                this._updateProgressComponent('transcoding', 10);\n                this._downloadFile(audioBlob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".webm\"));\n                return;\n            }\n            // Wait for FFmpeg loading to complete\n            await ffmpegLoadPromise;\n            // Wait for audio download to complete\n            const [audioBlob, imageBlob] = await Promise.all([\n                audioDownloadPromise,\n                thumbnailPromise\n            ]);\n            // Start transcoding\n            this._updateProgressComponent('transcoding', 0);\n            // Transcode audio (progress handled by transcoder callback)\n            const transcodedData = await this.transcoder.transcodeAudio(audioBlob, imageBlob, requestFormat, {\n                title: songData.title,\n                artist: songData.artist,\n                album: songData.album\n            });\n            // Transcoding complete\n            this._updateProgressComponent('transcoding', 10);\n            // Create and download transcoded file\n            const blob = new Blob([\n                transcodedData\n            ], {\n                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'\n            });\n            this._downloadFile(blob, \"\".concat(songData.title, \" - \").concat(songData.artist, \".\").concat(requestFormat));\n            this._updateProgressComponent('transcoding', 10);\n        } catch (error) {\n            if (error.name === 'AbortError') {\n                throw new Error(t1(\"download_cancelled\"));\n            }\n            throw error;\n        }\n    }\n    /**\n     * Load FFmpeg with progress updates\n     */ async _loadFFmpegWithProgress() {\n        this._updateProgressComponent('ffmpegLoad', 0);\n        await this.transcoder.load();\n        this._updateProgressComponent('ffmpegLoad', 10);\n        return true;\n    }\n    /**\n     * Skip FFmpeg loading for webm format\n     */ async _skipFFmpegLoad() {\n        this._updateProgressComponent('ffmpegLoad', 10);\n        return true;\n    }\n    /**\n     * Get download URL with retries and progress updates\n     */ async _getDownloadUrlWithProgress(songData) {\n        const maxRetries = 20;\n        const retryDelay = 2000;\n        for(let retries = 0; retries < maxRetries; retries++){\n            if (this.abortController.signal.aborted) {\n                throw new Error('Download cancelled');\n            }\n            // Update progress based on retry attempts (0-20% range)\n            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));\n            this._updateProgressComponent('urlFetch', progressStep);\n            try {\n                const status = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fetchDownloadStatus)(songData, 'download');\n                if (status.download_url) {\n                    const isReady = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.checkDownloadLink)(status.download_url);\n                    if (isReady) {\n                        this._updateProgressComponent('urlFetch', 20);\n                        return status.download_url;\n                    }\n                }\n            } catch (error) {\n                console.warn(\"Download URL fetch attempt \".concat(retries + 1, \" failed:\"), error);\n            }\n            // Wait before retry\n            await this._delay(retryDelay);\n        }\n        throw new Error(t(\"download_timeout\"));\n    }\n    /**\n     * Fetch audio file with progress tracking using axios\n     */ async _fetchAudioWithProgress(url) {\n        try {\n            this._updateProgressComponent('audioDownload', 0);\n            const response = await (0,axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                method: 'GET',\n                url: url,\n                responseType: 'blob',\n                signal: this.abortController.signal,\n                onDownloadProgress: (progressEvent)=>{\n                    if (progressEvent.lengthComputable) {\n                        // Map download progress to 0-50% range for audioDownload component\n                        const downloadPercent = progressEvent.loaded / progressEvent.total * 100;\n                        const mappedProgress = downloadPercent / 100 * 50; // 0-50%\n                        this._updateProgressComponent('audioDownload', mappedProgress);\n                    } else {\n                        // If we can't track progress, show incremental updates\n                        const currentTime = Date.now();\n                        if (!this.downloadStartTime) {\n                            this.downloadStartTime = currentTime;\n                        }\n                        const elapsed = (currentTime - this.downloadStartTime) / 1000;\n                        const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%\n                        this._updateProgressComponent('audioDownload', estimatedProgress);\n                    }\n                }\n            });\n            // Download completed\n            this._updateProgressComponent('audioDownload', 50);\n            return response.data;\n        } catch (error) {\n            if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {\n                throw new Error(t(\"download_cancelled\"));\n            }\n            throw new Error(t(\"failed_to_download_audio\"));\n        }\n    }\n    /**\n     * Fetch audio file with error handling (fallback method)\n     */ async _fetchAudio(url, t1) {\n        const response = await fetch(url, {\n            signal: this.abortController.signal\n        });\n        if (!response.ok) {\n            throw new Error(t1(\"failed_to_download_audio\"));\n        }\n        return response.blob();\n    }\n    /**\n     * Fetch thumbnail with graceful failure\n     */ async _fetchThumbnail(thumbnailUrl) {\n        try {\n            const response = await fetch(thumbnailUrl, {\n                signal: this.abortController.signal\n            });\n            return response.ok ? response.blob() : null;\n        } catch (error) {\n            console.warn('Thumbnail fetch failed:', error);\n            return null;\n        }\n    }\n    /**\n     * Download file to user's device\n     */ _downloadFile(blob, filename) {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        a.style.display = 'none';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        // Clean up object URL\n        setTimeout(()=>URL.revokeObjectURL(url), 100);\n    }\n    /**\n     * Utility delay function\n     */ _delay(ms) {\n        return new Promise((resolve)=>{\n            const timeoutId = setTimeout(resolve, ms);\n            // Allow cancellation\n            this.abortController.signal.addEventListener('abort', ()=>{\n                clearTimeout(timeoutId);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Cancel ongoing download\n     */ cancel() {\n        if (this.abortController) {\n            this.abortController.abort();\n        }\n    }\n    /**\n     * Clean up resources\n     */ cleanup() {\n        this.cancel();\n        if (this.transcoder) {\n            this.transcoder.terminate();\n            this.transcoder = null;\n        }\n        this.abortController = null;\n    }\n    constructor(){\n        this.transcoder = null;\n        this.abortController = null;\n        this.progressComponentCallback = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/utils/downloadManager.js\n"));

/***/ })

});