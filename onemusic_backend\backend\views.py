import hashlib
import smtplib

import requests
from django.conf import settings
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.models import User
from django.core.cache import cache
from django.core.mail import BadHeaderError, send_mail
from django.http import JsonResponse, HttpResponseBadRequest
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from django.utils.translation import gettext as _

from .models import Profile
import json

from backend.utils import generate_jwt, verify_jwt, get_random_music


@csrf_exempt
def send_verification_email(request):
    """发送验证邮件，增加 Turnstile 验证"""
    data = json.loads(request.body)
    user_email = data.get('email')
    user_password = data.get('password')
    turnstile_token = data.get('captchaToken')

    if not user_email or not user_password or not turnstile_token:
        return JsonResponse({"message": _("邮箱、密码和验证码不能为空")}, status=400)

    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()  # 取第一个 IP
    else:
        ip = request.META.get('REMOTE_ADDR', '')

    # 验证 Turnstile 令牌
    turnstile_secret = settings.TURNSTILE_SECRET_KEY
    turnstile_url = "https://challenges.cloudflare.com/turnstile/v0/siteverify"
    turnstile_data = {
        "secret": turnstile_secret,
        "response": turnstile_token,
        "remoteip": ip
    }
    response = requests.post(turnstile_url, data=turnstile_data)
    turnstile_result = response.json()

    if not turnstile_result.get("success", False):
        return JsonResponse({"message": _("验证码验证失败，请重试")}, status=400)
    # 密码用 sha256 哈希加密存储
    password_hash = hashlib.sha256(user_password.encode('utf-8')).hexdigest()

    # 生成 JWT Token，携带邮箱和密码哈希
    token = generate_jwt({"email": user_email, "password": password_hash})

    # 构建验证链接
    verification_url = request.build_absolute_uri(reverse('verify_email')) + f"?token={token}"

    # 尝试发送邮件
    try:
        send_mail(
            subject='邮箱验证',
            message=f"{_("点击以下链接验证邮箱(30 分钟内有效)")}：\n{verification_url}",
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user_email],
        )
    except BadHeaderError:
        return JsonResponse({"message": _("邮件头错误，发送失败")}, status=500)
    except smtplib.SMTPException as e:
        return JsonResponse({"message": f"{_("邮件发送失败，请稍后再试。错误信息：")}{str(e)}"}, status=500)

    return JsonResponse({"message": _("验证邮件已发送，请查收。")})

@csrf_exempt
def verify_email(request):
    """验证邮箱链接"""
    token = request.GET.get('token')
    decoded_payload = verify_jwt(token)

    if not decoded_payload:
        return render(request, "error.html", {"message": "Invalid or expired link"})

    email = decoded_payload.get('email')
    password_hash = decoded_payload.get('password')

    if not email or not password_hash:
        return render(request, "error.html", {"message": "Incomplete verification data"})

    # 检查用户是否已存在
    user = User.objects.filter(username=email).first()
    if user:
        # 更新用户密码
        user.set_password(password_hash)
        user.save()
        return render(request, "success.html", {"message": f"{email} Password updated!"})

    # 创建用户
    user = User.objects.create_user(username=email, password=password_hash)
    Profile.objects.create(user=user)

    return render(request, "success.html", {"message": f"{email} Verification successful! User registered"})

@csrf_exempt
def login_view(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        email = data.get('email')
        password = hashlib.sha256(data.get('password').encode('utf-8')).hexdigest()
        user = authenticate(request, username=email, password=password)
        if user is not None:
            login(request, user)
            res = JsonResponse({'detail': '登录成功'})
            return res
        else:
            return HttpResponseBadRequest(json.dumps({'detail': _("邮箱或密码错误")}), content_type='application/json')

@csrf_exempt
def logout_view(request):
    if request.method == 'POST':
        logout(request)
        return JsonResponse({'detail': '登出成功'})

@csrf_exempt
@login_required(login_url='/login')
def get_user_profile_view(request):
    from .webdav import generate_webdav_jwt

    profile = get_object_or_404(Profile.objects.prefetch_related("webdav_config"), user=request.user)

    # 处理 WebDAV 配置信息，包含签名
    webdav_configs = [
        {
            "id": config.id,
            "url": config.url,
            "username": config.username,
            "password": config.password,  # 包含密码用于WebDAV服务
            "signature": config.signature,  # 预生成的签名
            "isTested": True
        }
        for config in profile.webdav_config.all()
    ]

    # 生成WebDAV JWT
    webdav_jwt = generate_webdav_jwt(request.user.email)

    # 组织返回数据
    data = {
        "username": profile.user.username,
        "email": request.user.email,
        "points": profile.points,
        "webdav_configs": webdav_configs,  # 包含带签名的WebDAV配置信息
        "webdav_jwt": webdav_jwt  # WebDAV JWT令牌
    }

    return JsonResponse(data)

@csrf_exempt
def cached_random_music(request):
    cache_key = "random_music_list"
    cache_timeout = 120  # 缓存 2 分钟

    # 尝试从缓存中获取数据
    music_list = cache.get(cache_key)

    if not music_list:
        # 如果缓存不存在，则获取新数据并存入缓存
        music_list = get_random_music()
        cache.set(cache_key, music_list, cache_timeout)

    return JsonResponse({"music_list": music_list})


